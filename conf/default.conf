server {
    listen       80;
    server_name  localhost;

    # # 静态资源预览
    # location /cpfs-nfs/sais/data-plaza-svc/ {
    #     alias /cpfs-nfs/sais/data-plaza-svc/;

    #     autoindex on;
    #     autoindex_exact_size off;
    #     autoindex_localtime on;

    #     add_header Content-Disposition "inline";   # 强制预览
    #     # add_header Content-Type "application/octet-stream";  # 配合Content-Disposition
    #     add_header Access-Control-Allow-Origin *;
    #     add_header Access-Control-Allow-Methods "GET, OPTIONS";

    #     # 禁止直接访问 full download 目录
    #     location ~* ^/cpfs-nfs/sais/data-plaza-svc/datasets/[^/]+/full/download/ {
    #         return 403;
    #     }
    # }

    # # 下载
    # location /download/cpfs-nfs/sais/data-plaza-svc/ {
    #     # 访问前执行 Lua 脚本校验
    #     access_by_lua_file /etc/nginx/conf.d/verify_token.lua;

    #     alias /cpfs-nfs/sais/data-plaza-svc/;

    #      # 禁用缓存
    #     add_header Cache-Control "no-cache, no-store, must-revalidate";
    #     add_header Pragma "no-cache";
    #     add_header Expires "0";

    #     add_header Content-Type application/octet-stream;
    #     add_header Content-Disposition "attachment";
    #     add_header Access-Control-Allow-Origin *;
    #     add_header Access-Control-Expose-Headers Content-Disposition;
    #     add_header Access-Control-Allow-Methods "GET, OPTIONS";
    # }

    # location /simple-download/cpfs-nfs/sais/data-plaza-svc/ {
    #     alias /cpfs-nfs/sais/data-plaza-svc/;

    #      # 禁用缓存
    #     add_header Cache-Control "no-cache, no-store, must-revalidate";
    #     add_header Pragma "no-cache";
    #     add_header Expires "0";

    #     add_header Content-Type application/octet-stream;
    #     add_header Content-Disposition "attachment";
    #     add_header Access-Control-Allow-Origin *;
    #     add_header Access-Control-Expose-Headers Content-Disposition;
    #     add_header Access-Control-Allow-Methods "GET, OPTIONS";

    #     # 禁止直接访问 full download 目录
    #     location ~* ^/simple-download/cpfs-nfs/sais/data-plaza-svc/datasets/[^/]+/full/download/ {
    #         return 403;
    #     }
    # }

    location / {
        root   html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}