-- local aes = require "resty.aes"
-- local cjson = require "cjson"

-- -- AES 配置（必须与后端保持一致）
-- local AES_KEY = "a1b2c3d4e5f6a7b8"
-- local AES_IV = "1234567890123456"       -- 16字节初始向量

-- -- 从URL参数获取token
-- local args = ngx.req.get_uri_args()
-- local encrypted_token = args["token"]

-- if not encrypted_token then
--     ngx.log(ngx.ERR, "Missing token parameter")
--     ngx.exit(ngx.HTTP_BAD_REQUEST)
-- end


-- -- 初始化 AES 解密器
-- local aes_128_cbc, err = aes:new(AES_KEY, nil, aes.cipher(128, "cbc"), {iv = AES_IV})

-- -- 检查初始化是否失败
-- if not aes_128_cbc then
--     ngx.log(ngx.ERR, "Failed to create AES instance: ", err)
--     ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)
-- end

-- -- 解密令牌
-- local decrypted = aes_128_cbc:decrypt(ngx.decode_base64(encrypted_token))
-- if not decrypted then
--     ngx.log(ngx.ERR, "Failed to decrypt token")
--     ngx.exit(ngx.HTTP_UNAUTHORIZED)
-- end

-- -- 解析 JSON
-- local token_data
-- local status, err = pcall(function()
--     token_data = cjson.decode(decrypted)
-- end)

-- if not status or not token_data then
--     ngx.log(ngx.ERR, "Invalid token format: ", err)
--     ngx.exit(ngx.HTTP_UNAUTHORIZED)
-- end

-- -- 校验有效期
-- local expire_time = tonumber(token_data.expire)
-- ngx.log(ngx.ERR, "Token expire_time:", expire_time)

-- if not expire_time or os.time() * 1000 > expire_time then
--     ngx.log(ngx.ERR, "Token expired", os.time() * 1000)
--     ngx.exit(ngx.HTTP_UNAUTHORIZED)
-- end

-- -- 验证通过，继续处理请求
-- return