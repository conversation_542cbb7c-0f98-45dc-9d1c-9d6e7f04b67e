<svg width="318" height="178" viewBox="0 0 318 178" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_314_3404" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="318" height="178">
<rect width="318" height="178" rx="7" fill="url(#paint0_linear_314_3404)"/>
</mask>
<g mask="url(#mask0_314_3404)">
<mask id="mask1_314_3404" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-1" y="-1" width="320" height="180">
<rect x="-1" y="-1" width="320" height="180" rx="8" fill="url(#paint1_linear_314_3404)"/>
</mask>
<g mask="url(#mask1_314_3404)">
<g filter="url(#filter0_d_314_3404)">
<rect x="17" y="53" width="317" height="188" rx="6" fill="white" fill-opacity="0.5" shape-rendering="crispEdges"/>
<rect x="17.5" y="53.5" width="316" height="187" rx="5.5" stroke="white" shape-rendering="crispEdges"/>
</g>
<rect x="31.5" y="67.5" width="302" height="19" fill="#ECECEC" stroke="#E4E4E4"/>
<rect x="31.5" y="86.5" width="302" height="19" fill="white" stroke="#E4E4E4"/>
<rect x="31.5" y="105.5" width="302" height="19" fill="white" stroke="#E4E4E4"/>
<rect x="31.5" y="143.5" width="302" height="19" fill="white" stroke="#E4E4E4"/>
<rect x="31.5" y="124.5" width="302" height="19" fill="white" stroke="#E4E4E4"/>
<rect x="31.5" y="162.5" width="302" height="19" fill="white" stroke="#E4E4E4"/>
</g>
<path d="M109.5 67L109.5 181" stroke="#E4E4E4"/>
</g>
<defs>
<filter id="filter0_d_314_3404" x="-13" y="23" width="377" height="248" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.333966 0 0 0 0 0.189998 0 0 0 0 0.047428 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_314_3404"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_314_3404" result="shape"/>
</filter>
<linearGradient id="paint0_linear_314_3404" x1="159" y1="0" x2="159" y2="178" gradientUnits="userSpaceOnUse">
<stop stop-color="#F1C097"/>
<stop offset="1" stop-color="#F1C097"/>
</linearGradient>
<linearGradient id="paint1_linear_314_3404" x1="159" y1="-1" x2="159" y2="179" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2F2FF"/>
<stop offset="1" stop-color="#D6D2F9"/>
</linearGradient>
</defs>
</svg>
