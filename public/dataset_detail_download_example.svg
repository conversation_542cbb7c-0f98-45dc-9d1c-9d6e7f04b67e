<svg width="322" height="178" viewBox="0 0 322 178" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_314_3386)">
<mask id="mask0_314_3386" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="318" height="178">
<rect width="318" height="178" rx="7" fill="url(#paint0_linear_314_3386)"/>
</mask>
<g mask="url(#mask0_314_3386)">
<g opacity="0.5">
<g filter="url(#filter1_d_314_3386)">
<rect x="250" y="53" width="217" height="108" rx="6" fill="white" fill-opacity="0.5" shape-rendering="crispEdges"/>
<rect x="250.5" y="53.5" width="216" height="107" rx="5.5" stroke="white" shape-rendering="crispEdges"/>
</g>
<rect x="258" y="61" width="201" height="92" rx="6" fill="white"/>
<path d="M264 73C264 69.6863 266.686 67 270 67H447C450.314 67 453 69.6863 453 73V100C453 103.314 450.314 106 447 106H270C266.686 106 264 103.314 264 100V73Z" fill="url(#paint1_linear_314_3386)"/>
<path d="M264 116C264 113.791 265.791 112 268 112H449C451.209 112 453 113.791 453 116V123C453 125.209 451.209 127 449 127H268C265.791 127 264 125.209 264 123V116Z" fill="#E7E7E7" fill-opacity="0.8"/>
<path d="M264 136C264 133.791 265.791 132 268 132H449C451.209 132 453 133.791 453 136V143C453 145.209 451.209 147 449 147H268C265.791 147 264 145.209 264 143V136Z" fill="#EFEFEF" fill-opacity="0.68"/>
</g>
<g filter="url(#filter2_d_314_3386)">
<rect x="17" y="53" width="217" height="108" rx="6" fill="white" fill-opacity="0.5" shape-rendering="crispEdges"/>
<rect x="17.5" y="53.5" width="216" height="107" rx="5.5" stroke="white" shape-rendering="crispEdges"/>
</g>
<rect x="25" y="61" width="201" height="92" rx="6" fill="white"/>
<path d="M31 73C31 69.6863 33.6863 67 37 67H214C217.314 67 220 69.6863 220 73V100C220 103.314 217.314 106 214 106H37C33.6863 106 31 103.314 31 100V73Z" fill="url(#paint2_linear_314_3386)"/>
<path d="M31 116C31 113.791 32.7909 112 35 112H216C218.209 112 220 113.791 220 116V123C220 125.209 218.209 127 216 127H35C32.7909 127 31 125.209 31 123V116Z" fill="#E7E7E7" fill-opacity="0.8"/>
<path d="M31 136C31 133.791 32.7909 132 35 132H216C218.209 132 220 133.791 220 136V143C220 145.209 218.209 147 216 147H35C32.7909 147 31 145.209 31 143V136Z" fill="#EFEFEF" fill-opacity="0.68"/>
</g>
</g>
<defs>
<filter id="filter0_d_314_3386" x="13" y="53" width="309" height="116" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.85098 0 0 0 0 0.85098 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_314_3386"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_314_3386" result="shape"/>
</filter>
<filter id="filter1_d_314_3386" x="220" y="23" width="277" height="168" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.10339 0 0 0 0 0.0784312 0 0 0 0 0.390418 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_314_3386"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_314_3386" result="shape"/>
</filter>
<filter id="filter2_d_314_3386" x="-13" y="23" width="277" height="168" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.10339 0 0 0 0 0.0784312 0 0 0 0 0.390418 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_314_3386"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_314_3386" result="shape"/>
</filter>
<linearGradient id="paint0_linear_314_3386" x1="159" y1="0" x2="159" y2="178" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2F2FF"/>
<stop offset="1" stop-color="#D6D2F9"/>
</linearGradient>
<linearGradient id="paint1_linear_314_3386" x1="264" y1="86.5" x2="451" y2="86.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2F6FF"/>
<stop offset="1" stop-color="#D8E4FC"/>
</linearGradient>
<linearGradient id="paint2_linear_314_3386" x1="31" y1="86.5" x2="218" y2="86.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2F6FF"/>
<stop offset="1" stop-color="#D8E4FC"/>
</linearGradient>
</defs>
</svg>
