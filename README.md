# high-altitude-ui

高原习服-前端

一个基于 React + TypeScript + Vite 构建的前端项目。

## 快速开始

### 环境要求
- Node.js >= 18
- pnpm >= 8

### 启动项目

1. 克隆仓库
`git clone https://github.com/your-repo-url.git`

2. 安装依赖
`pnpm install`

3. 启动开发服务器
`pnpm dev`

4. 环境变量配置

`.env.development` / `.env.production`

`VITE_API_BASE_URL=https://your-api-domain.com/api`

## 技术栈
- 框架: React 18
- 构建工具: Vite
- 语言: TypeScript
- 路由管理: React Router v6
- 状态管理: React Context
- 表单管理: react-hook-form + zod
- 请求库: axios + React Query（已封装统一请求与错误处理）
- 认证机制: access_token + refresh_token 双令牌机制
- UI 框架: Tailwind CSS + shadcn/ui
- 组件动画: Framer Motion
- 邮箱服务: Resend（支持重置密码邮件发送）

## 项目结构

```
├── src/
│   ├── assets/             # 静态资源
│   ├── components/         # 通用组件
│   ├── contexts/           # React Context（含 AuthContext 认证上下文）
│   ├── hooks/              # 自定义 hooks（如 useAuth, useUserQuery 等）
│   ├── lib/                # axios 实例封装、工具函数等
│   ├── pages/              # 页面级组件
│   ├── routes/             # 路由定义
│   ├── types/              # 全局类型定义
│   ├── utils/              # 工具方法
│   ├── App.tsx             # 应用入口
│   └── main.tsx            # Vite 启动入口
├── public/                 # 公共资源
├── .env                    # 环境变量配置
├── index.html              # HTML 模板
├── tailwind.config.ts      # Tailwind 配置
├── tsconfig.json           # TypeScript 配置
└── README.md               # 项目文档
```
