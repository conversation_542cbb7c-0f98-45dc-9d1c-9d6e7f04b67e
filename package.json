{"name": "vite-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "lint": "eslint src --fix", "format": "prettier --write src", "prepare": "husky", "preview": "vite preview"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "**/*.{json,md,html,css,scss}": ["prettier --write"]}, "dependencies": {"3dmol": "^2.5.0", "@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-slot": "^1.2.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.72.1", "@tanstack/react-table": "^8.21.3", "@types/lodash": "^4.17.17", "@types/react-virtualized-auto-sizer": "^1.0.8", "@types/react-window": "^1.8.8", "antd": "^5.24.9", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "lodash": "^4.17.21", "lucide": "^0.487.0", "lucide-react": "^0.487.0", "openchemlib": "^9.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.6.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tanstack/react-query-devtools": "^5.72.0", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af"}