import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import '@ant-design/v5-patch-for-react-19'
import './index.css'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'sonner'
import { BrowserRouter } from 'react-router-dom'
import { AppRoutes } from '@/routes'
import { AuthProvider } from './contexts/AuthContext'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import './i18n'

const queryClient = new QueryClient()

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AuthProvider>
      <ConfigProvider locale={zhCN}>
        <QueryClientProvider client={queryClient}>
          <BrowserRouter>
            <AppRoutes />
          </BrowserRouter>
          <ReactQueryDevtools initialIsOpen={false} />
          <Toaster richColors position="top-center" />
        </QueryClientProvider>
      </ConfigProvider>
    </AuthProvider>
  </StrictMode>,
)
