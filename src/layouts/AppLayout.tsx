import { Outlet, useLocation } from 'react-router-dom'
import { menuList } from '@/constants/menu'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Role } from '@/types/user'
import type { MenuProps } from 'antd'
import { Menu } from 'antd'
import AltitudeIcon from '@/assets/logo.svg'

import './AppLayout.css'

// 引入Ant Design的箭头图标（或使用内置箭头）
import { DownOutlined } from '@ant-design/icons'
import { useEffect, useState } from 'react'

export default function AppLayout() {
  const { token, user } = useAuth()
  const navigate = useNavigate()
  // const [current, setCurrent] = useState('/');
  const location = useLocation() // 获取当前路由信息
  const handleMenuClick: MenuProps['onClick'] = e => {
    console.log('click ', e)
    // setCurrent(e.key);
    navigate(e.key)
  }
  const [openKeys, setOpenKeys] = useState<string[]>([])
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const handleOpenChange: MenuProps['onOpenChange'] = keys => {
    setOpenKeys(keys)
  }

  // 菜单过滤逻辑：
  // 1. 未登录时，仅显示所有普通用户（role: 'user'）可见的菜单（或 roles 未定义的菜单）
  // 2. 登录后，按当前用户 role 显示所有有权限的菜单
  let visibleMenu = []
  if (!token) {
    visibleMenu = menuList.filter(item => !item.roles || item.roles.includes('user'))
  } else {
    visibleMenu = menuList.filter(item => !item.roles || item.roles.includes(user?.role as Role))
  }
  // 监听路由变化，更新选中状态
  useEffect(() => {
    const currentPath = location.pathname
    const keys: string[] = [currentPath]

    // 检查是否是子菜单路径，如果是，添加父级菜单的 key
    menuList.forEach(menu => {
      if (menu.children) {
        const childMatch = menu.children.find(child => child.path === currentPath)
        if (childMatch) {
          keys.push(menu.path) // 添加父级菜单路径
        }
      }
    })

    setSelectedKeys(keys)
  }, [location.pathname])

  // 判断当前是否是首页
  // const isHomePage = location.pathname === '/'
  //子菜单
  const menuItems = visibleMenu.map(item => {
    // const isAnalysisTool = item.label === "Analysis tool" && item.children?.length;
    return {
      key: item.path,
      label: (
        <span
          style={{ display: 'flex', alignItems: 'center', gap: '8px', verticalAlign: 'middle' }}
        >
          {item.label}
          {/* {isAnalysisTool && (openKeys.includes(item.path) ? <DownOutlined /> : <RightOutlined />)} */}
          {item.children?.length && <DownOutlined />}
        </span>
      ),
      children: item.children?.map(child => ({
        key: child.path,
        label: child.label,
      })),
    }
  })
  return (
    <div
      className="min-h-screen min-w-screen bg-white flex flex-col app-layout-container"
      // style={{
      //   backgroundImage: isHomePage ? `url(${Background})` : 'none',
      //   backgroundRepeat: 'no-repeat',
      //   backgroundPosition: 'top center',
      //   backgroundSize: '100% 960px',
      // }}
    >
      <header className="flex gap-4 justify-between items-center z-10 h-14 pl-4 pr-4 header fixed top-0 left-0 right-0 bg-white">
        <div className="logo" onClick={() => navigate('/')}>
          {/* <div className="icon bg-[url(/logo.svg)] bg-no-repeat bg-center bg-contain"></div> */}
          <img
            src={AltitudeIcon}
            className="icon bg-no-repeat bg-center bg-contain"
            style={{ width: '64px', height: '61px' }}
          ></img>
          <div className="name">
            <div className="name-text">High-Altitude Acclimatization</div>
            <div className="name-text">Longitudinal Cohort Database</div>
          </div>
        </div>
        <div className="menu">
          <Menu
            onClick={handleMenuClick}
            mode="horizontal"
            items={menuItems}
            selectedKeys={selectedKeys} // 动态选中状态
            openKeys={openKeys} // 控制子菜单展开
            onOpenChange={handleOpenChange} // 监听子菜单展开收起
            overflowedIndicator={null} // 禁用溢出指示器
            style={{
              flex: 1,
              justifyContent: 'center',
            }}
          />
        </div>
        <div className="action"></div>
        {/* <AccountMenu /> */}
        {/* <Button type="primary" onClick={() => navigate('/login')}>Login</Button> */}
      </header>
      <main className="flex mt-[90px]" style={{ height: 'calc(100vh - 160px)', overflow: 'auto' }}>
        <Outlet />
      </main>
      <div className=" border-t-gray-200 border-1 h-[70px] bg-gray-50 w-full">
        {/*footer部分 */}
        <div className="h-full flex items-center justify-center">
          Copyright © 1998 - 2025 Tencent. All Rights Reserved. High-Altitude Acclimatization
          Longitudinal Cohort Database
        </div>
      </div>
    </div>
  )
}
