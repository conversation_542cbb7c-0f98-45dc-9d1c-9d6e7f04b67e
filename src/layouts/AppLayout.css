.app-layout-container {
  .header {
    height: 90px;
    padding-right: 60px;
    padding-left: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: white;
    z-index: 1000;

    .logo {
      display: flex;
      align-items: center;
      gap: 12px;

      .icon {
        width: 45px;
        height: 49px;
      }

      .name {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0%;
        vertical-align: middle;
      }
    }

    .menu {
      display: flex;
      align-items: center;
      gap: 90px;
      flex: 1;
      justify-content: center;

      height: 40px;
      background: none;

      .ant-menu {
        background: none;
        width: 100%;
        justify-content: center;
      }

      :where(.css-dev-only-do-not-override-1m63z2v) a {
        color: inherit;
      }

      .ant-menu-horizontal {
        border-bottom-color: transparent !important;
        border-bottom: 0px;

        /* 禁用溢出处理 */
        .ant-menu-overflow {
          display: flex !important;
          flex-wrap: nowrap !important;
        }

        .ant-menu-overflow-item {
          flex-shrink: 0 !important;
        }

        /* 隐藏溢出指示器 */
        .ant-menu-overflow-item-suffix {
          display: none !important;
        }
      }

      /* && {
        ${text(14, 22, variables.firstClassText, variables.fontRegular)}
        min-width: 460px;
      } */
      .ant-menu-overflow-item {
        line-height: 40px;
      }

      .ant-menu-item {
        padding: 0 40px;
        min-width: 136px;
      }

      .ant-menu-submenu {
        padding: 0 40px;
      }

      li::after {
        border-bottom-color: transparent !important;
        border-bottom: 0px;
      }

      :global {
        &.ant-menu-horizontal {
          border-bottom: none !important;
        }
      }
    }
  }
}
