import React, { useState } from 'react'
import ReportDownload from './tool/components/report-download'
import { message, Modal } from 'antd'

export default function ReportDownloadDemo() {
  const [currentStep, setCurrentStep] = useState(3)
  const [showOptimizationModal, setShowOptimizationModal] = useState(false)

  const handlePrevious = () => {
    message.info('Previous step clicked')
    setCurrentStep(prev => Math.max(1, prev - 1))
  }

  const handleNext = () => {
    message.success('Next step clicked')
    setCurrentStep(prev => Math.min(5, prev + 1))
  }

  const handleFinish = () => {
    setShowOptimizationModal(true)
  }

  const optimizationOptions = [
    '继续优化图表样式和交互效果',
    '添加更多数据可视化选项',
    '优化表格性能和分页功能',
    '添加数据导出格式选择',
    '完善响应式设计',
    '最终确认完成',
  ]

  return (
    <div
      style={{
        padding: '48px',
        backgroundColor: '#f5f5f5',
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'flex-start',
      }}
    >
      <div
        style={{
          backgroundColor: 'white',
          padding: '48px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          width: '100%',
          maxWidth: '1400px',
        }}
      >
        <div
          style={{
            marginBottom: '32px',
            textAlign: 'center',
            color: 'rgba(0,0,0,0.85)',
          }}
        >
          <h1 style={{ fontSize: '24px', fontWeight: 500, margin: 0 }}>
            Report Download - Step {currentStep}
          </h1>
          <p
            style={{
              fontSize: '14px',
              color: 'rgba(0,0,0,0.45)',
              margin: '8px 0 0 0',
            }}
          >
            数据分析结果可视化和下载页面
          </p>
        </div>

        <ReportDownload onPrevious={handlePrevious} onNext={handleNext} />

        <div
          style={{
            marginTop: '32px',
            display: 'flex',
            justifyContent: 'center',
            gap: '16px',
          }}
        >
          <button
            onClick={handlePrevious}
            style={{
              padding: '8px 24px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              background: 'white',
              cursor: 'pointer',
            }}
          >
            Previous
          </button>
          <button
            onClick={handleFinish}
            style={{
              padding: '8px 24px',
              border: '1px solid #1975ff',
              borderRadius: '4px',
              background: '#1975ff',
              color: 'white',
              cursor: 'pointer',
            }}
          >
            完成实现
          </button>
        </div>

        <div
          style={{
            marginTop: '32px',
            padding: '16px',
            backgroundColor: '#f8f9fa',
            borderRadius: '6px',
            fontSize: '14px',
            color: 'rgba(0,0,0,0.65)',
          }}
        >
          <h4 style={{ margin: '0 0 8px 0', color: 'rgba(0,0,0,0.85)' }}>已实现功能:</h4>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>✅ 1:1 还原设计稿布局和样式</li>
            <li>✅ 三个配置选择器（Ant Design Select组件）</li>
            <li>✅ Generate 按钮和 Download 按钮</li>
            <li>✅ 图表展示区域（使用占位图片）</li>
            <li>✅ 数据分析结果表格（支持横向和纵向滚动）</li>
            <li>✅ 响应式设计适配</li>
            <li>✅ 完整的交互功能</li>
          </ul>
        </div>
      </div>

      <Modal
        title="选择下一步操作"
        open={showOptimizationModal}
        onCancel={() => setShowOptimizationModal(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: '16px 0' }}>
          <p style={{ marginBottom: '16px', color: 'rgba(0,0,0,0.65)' }}>
            Report Download 组件已基本完成，请选择您希望的下一步操作：
          </p>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {optimizationOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => {
                  message.info(`选择了: ${option}`)
                  setShowOptimizationModal(false)
                }}
                style={{
                  padding: '12px 16px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  background: 'white',
                  textAlign: 'left',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.borderColor = '#1975ff'
                  e.currentTarget.style.backgroundColor = '#f0f8ff'
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.borderColor = '#d9d9d9'
                  e.currentTarget.style.backgroundColor = 'white'
                }}
              >
                {option}
              </button>
            ))}
          </div>
        </div>
      </Modal>
    </div>
  )
}
