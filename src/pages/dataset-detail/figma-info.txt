/* Frame 2053141785 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 24px;

position: absolute;
width: 945px;
height: 806px;
left: calc(50% - 945px/2 + 199.5px);
top: 138px;



/* Frame 2053141770 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 24px;

width: 500px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141769 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 16px;

width: 396px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 选择框 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

width: 160px;
height: 32px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141472 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px 12px;

width: 160px;
height: 32px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 125.82px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: 11.09px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.59%;
right: 7.59%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* 提示说明文本：占位符 */

display: none;
width: 140px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
display: flex;
align-items: center;

color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* 选择框 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

width: 220px;
height: 32px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 2053141472 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px 12px;

width: 220px;
height: 32px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* title */

width: 185.82px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: 11.09px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.59%;
right: 7.59%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* 提示说明文本：占位符 */

display: none;
width: 140px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
display: flex;
align-items: center;

color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* button */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 80px;
height: 32px;

border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Button */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px 24px;
gap: 4px;

width: 80px;
height: 32px;

background: #1675FF;
border: 1px solid #1675FF;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: center;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 45px;
height: 22px;

/* 段落（R）/P3 */
font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
text-align: center;

/* Fixed/F */
color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* 数据表格 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 20px;

width: 945px;
height: 750px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* table */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 945px;
height: 750px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 1;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 76px;
height: 750px;

background: #FFFFFF;
border-width: 1px 1px 0px 1px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 76px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 52px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
display: none;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

/* Conditional/divider */
border: 1px solid rgba(0, 0, 0, 0.06);
transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 83px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

display: none;
width: 83px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 23px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 7;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 8;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 25px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 25px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 9;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 10;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 11;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 12;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 13;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 14;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 76px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 15;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 26px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 750px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 395px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 208px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Frame 2053141771 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 18px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 32px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 41px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* text */

width: 45px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 52px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141772 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141773 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141776 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141777 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141778 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141779 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141780 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141781 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141782 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141783 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 11;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141784 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 12;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141785 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 13;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141786 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 14;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141787 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 395px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 15;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 27px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

display: none;
width: 120px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 16;
align-self: stretch;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 750px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 237px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* title */

width: 213px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Frame 2053141772 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 18px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 32px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 41px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141773 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141777 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141778 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141779 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141780 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141781 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141782 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141783 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141784 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141785 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 11;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141786 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 12;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141787 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 13;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141788 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 14;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141789 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 15;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 21px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

display: none;
width: 120px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 16;
align-self: stretch;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 750px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 237px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 213px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Frame 2053141772 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 18px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* text */

width: 32px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 41px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141773 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141777 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141778 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141779 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141780 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141781 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141782 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141783 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141784 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 10;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141785 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 11;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141786 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 12;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141787 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 13;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141788 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 14;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141789 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 237px;
height: 47px;


/* Inside auto layout */
flex: none;
order: 15;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 17px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 79px;
height: 47px;

/* Conditional/divider */
border-width: 0px 1px 1px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* text */

width: 26px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

display: none;
width: 120px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 16;
align-self: stretch;
flex-grow: 0;
