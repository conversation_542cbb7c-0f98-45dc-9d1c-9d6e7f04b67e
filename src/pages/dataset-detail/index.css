/* 根据设计稿精确还原的样式 */
.dataset-detail {
  width: 100%;
  display: flex;
  height: calc(100vh - 160px);
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  padding: 48px;
  gap: 24px;
  box-sizing: border-box;
  overflow-x: auto;
}

/* 左侧面板 - 固定宽度375px */
.left-panel {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  width: 375px;
  min-width: 375px;
  height: 100%;
  overflow-y: auto;
  background: #ffffff;
  flex-shrink: 0;
}

/* 信息区块 */
.info-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
  width: 100%;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

/* 区块标题头部 */
.section-header {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 0px 10px;
  gap: 6px;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

/* 区块图标 */
.section-icon {
  width: 16px;
  height: 16px;
  flex: none;
}

/* 区块标题文字 */
.section-title {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  flex: none;
}

/* 信息项容器 */
.info-items {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 12px;
  width: 100%;
}

/* 单个信息项 */
.info-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0px;
  gap: 4px;
  width: 100%;
}

/* 信息标签 */
.info-label {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.65);
  flex: none;
  white-space: nowrap;
}

/* 信息值 */
.info-value {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  flex: 1;
  word-break: break-all;
}

/* 链接样式 */
.info-link {
  color: rgba(0, 0, 0, 0.85);
  text-decoration: none;
  cursor: pointer;
}

.info-link:hover {
  color: #3971ff;
  text-decoration: underline;
}

/* 右侧面板 */
.right-panel {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 24px;
  flex: 1;
  min-width: 0;
  height: 100%;
  overflow-y: auto;
  background: #ffffff;
}

/* 控制区域 */
.controls-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 24px;
  width: 100%;
}

.controls-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 24px;
  max-width: 500px;
  height: 32px;
}

/* 选择框样式 */
.unit-select {
  width: 160px;
  height: 32px;
}

.sample-select {
  width: 220px;
  height: 32px;
}

.unit-select .ant-select-selector,
.sample-select .ant-select-selector {
  height: 32px !important;
  padding: 5px 12px !important;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
}

.unit-select .ant-select-selection-item,
.sample-select .ant-select-selection-item {
  line-height: 22px !important;
}

/* 搜索按钮样式 */
.search-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 24px;
  gap: 4px;
  width: 80px;
  height: 32px;
  background: #1675ff;
  border: 1px solid #1675ff;
  border-radius: 4px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  color: #ffffff;
}

.search-button:hover {
  background: #4096ff;
  border-color: #4096ff;
}

/* 数据表格区域 */
.data-table-section {
  flex: 1;
  width: 100%;
  min-width: 0;
  overflow: auto;
}

/* Ant Design Table 自定义样式 */
.dataset-table {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
}

.dataset-table .ant-table {
  font-size: 14px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  width: auto !important;
  min-width: 100%;
}

.dataset-table .ant-table table {
  table-layout: auto !important;
  width: auto !important;
  min-width: 100%;
}

.dataset-table .ant-table-thead > tr > th {
  background: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  padding: 12px;
  text-align: center;
}

.dataset-table .ant-table-thead > tr > th:last-child {
  border-right: none;
}

.dataset-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  padding: 12px;
  text-align: center;
}

.dataset-table .ant-table-tbody > tr > td:last-child {
  border-right: none;
}

.dataset-table .ant-table-tbody > tr:hover > td {
  background-color: rgba(0, 0, 0, 0.02);
}

.dataset-table .ant-table-container {
  border: none;
}

.dataset-table .ant-table-content {
  overflow-x: auto;
}

/* 表格滚动条样式 */
.dataset-table .ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.dataset-table .ant-table-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.dataset-table .ant-table-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.dataset-table .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
