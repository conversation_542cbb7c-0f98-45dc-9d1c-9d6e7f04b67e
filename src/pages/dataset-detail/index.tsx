import './index.css'
import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useDatasetDetailInfo } from '@/hooks/useDatasetQuery'
import { useDataPreview } from '@/hooks/useSearchQuery'
import { Button, Select, Spin, Table } from 'antd'
// import type { ColumnsType } from 'antd/es/table'
import BasicIcon from '@/assets/dataset/basic.svg'
import SourceIcon from '@/assets/dataset/source.svg'
import { convertToColumns } from '@/lib/utils'

const { Option } = Select

// Mock数据
// const mockBasicInfo = {
//   name: 'AltitudeOmics_Basic physiological information',
//   introduction:
//     'Individual body weight data reflect total body mass, composed of both body fat (%) and lean body mass (%).',
//   superclass: 'Brain',
//   class: 'Lake Louise Score (LLS) and AMS-C Score',
//   title:
//     'AltitudeOmics: the integrative physiology of human acclimatization to hypobaric hypoxia and its retention upon reascent',
//   author:
//     '<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>-<PERSON>uten S, <PERSON>, <PERSON>rk J, Kark S, <PERSON>ser B, Kern JP, <PERSON> SE, Lathan C, <PERSON> SS, Lovering AT, Paterson R, Polaner DM, Ryan BJ, Spira JL, Tsao JW, Wachsmuth NB, Roach RC.',
//   doi: '10.1371/journal.pone.0092191',
//   journal: 'PLoS One',
//   year: '2014',
//   pmid: '24658407',
//   url: 'https://journals.plos.org/plosone/article?id=10.1371/journal.pone.009219124658407',
// }

// 表格数据类型定义
// interface TableDataType {
//   key: string
//   id: string
//   bodyweight_SL: number | string
//   bodyweight_ALT1: number | string
//   bodyweight_ALT16: number | string
//   bodyweight_POST7: number | string
//   bodyweight_POST21: number | string
//   bodyFat_SL: number | string
//   bodyFat_ALT1: number | string
//   bodyFat_ALT16: number | string
//   leanBodyMass_SL: number | string
//   leanBodyMass_ALT1: number | string
//   leanBodyMass_ALT16: number | string
// }

// const mockTableData: TableDataType[] = [
//   {
//     key: '001',
//     id: '001',
//     bodyweight_SL: 80.8,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 76.8,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 78.6,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '002',
//     id: '002',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '003',
//     id: '003',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '004',
//     id: '004',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '005',
//     id: '005',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '006',
//     id: '006',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '007',
//     id: '007',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '008',
//     id: '008',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
//   {
//     key: '009',
//     id: '009',
//     bodyweight_SL: 65.4,
//     bodyweight_ALT1: '',
//     bodyweight_ALT16: 64.1,
//     bodyweight_POST7: '',
//     bodyweight_POST21: 65,
//     bodyFat_SL: 8.4,
//     bodyFat_ALT1: 8.5,
//     bodyFat_ALT16: 6.8,
//     leanBodyMass_SL: 74,
//     leanBodyMass_ALT1: '',
//     leanBodyMass_ALT16: 71.5,
//   },
// ]

// 表格列配置
// const mockColumns: ColumnsType<TableDataType> = [
//   {
//     title: 'ID',
//     dataIndex: 'id',
//     key: 'id',
//     width: 76,
//     align: 'center',
//   },
//   {
//     title: 'Bodyweight (kg)',
//     children: [
//       {
//         title: 'SL',
//         dataIndex: 'bodyweight_SL',
//         key: 'bodyweight_SL',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'ALT1',
//         dataIndex: 'bodyweight_ALT1',
//         key: 'bodyweight_ALT1',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'ALT16',
//         dataIndex: 'bodyweight_ALT16',
//         key: 'bodyweight_ALT16',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'POST7',
//         dataIndex: 'bodyweight_POST7',
//         key: 'bodyweight_POST7',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'POST21',
//         dataIndex: 'bodyweight_POST21',
//         key: 'bodyweight_POST21',
//         width: 79,
//         align: 'center',
//       },
//     ],
//   },
//   {
//     title: 'Body fat (%)',
//     children: [
//       {
//         title: 'SL',
//         dataIndex: 'bodyFat_SL',
//         key: 'bodyFat_SL',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'ALT1',
//         dataIndex: 'bodyFat_ALT1',
//         key: 'bodyFat_ALT1',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'ALT16',
//         dataIndex: 'bodyFat_ALT16',
//         key: 'bodyFat_ALT16',
//         width: 79,
//         align: 'center',
//       },
//     ],
//   },
//   {
//     title: 'Lean body mass(%)',
//     children: [
//       {
//         title: 'SL',
//         dataIndex: 'leanBodyMass_SL',
//         key: 'leanBodyMass_SL',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'ALT1',
//         dataIndex: 'leanBodyMass_ALT1',
//         key: 'leanBodyMass_ALT1',
//         width: 79,
//         align: 'center',
//       },
//       {
//         title: 'ALT16',
//         dataIndex: 'leanBodyMass_ALT16',
//         key: 'leanBodyMass_ALT16',
//         width: 79,
//         align: 'center',
//       },
//     ],
//   },
// ]

const DATA_PREVIEW_ALL = 1

export default function DatasetDetailPage() {
  const [searchParams] = useSearchParams()
  const id = searchParams.get('id') || ''

  // 获取详情数据
  const { data: detailInfo, isLoading } = useDatasetDetailInfo({
    params: {
      id,
    },
  })

  // 获取搜索数据
  const { data: searchData, isLoading: searchLoading } = useDataPreview({
    params: {
      id,
      module: 'dataset',
      all: DATA_PREVIEW_ALL,
    },
  })

  // 使用 API 数据或回退到 mock 数据
  const { source, basicInfo } = detailInfo || {
    source: {},
    basicInfo: {},
  }
  const tableData = searchData?.data || []
  const tableColumns = searchData?.header || []

  useEffect(() => {
    if (id) {
      console.log('Loading data for:', { id })
    }
  }, [id])
  const [unit, setUnit] = useState('all')
  const [sampleNumber, setSampleNumber] = useState('all')

  if (isLoading || searchLoading) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <Spin />
      </div>
    )
  }

  return (
    <div className="dataset-detail">
      {/* 左侧固定宽度区域 */}
      <div className="left-panel">
        {/* Basic Information */}
        <div className="info-section">
          <div className="section-header">
            <div className="section-icon">
              <img src={BasicIcon} alt="node" width={16} height={16} />
            </div>
            <span className="section-title">Basic information</span>
          </div>

          <div className="info-items">
            <div className="info-item">
              <span className="info-label">Name：</span>
              <span className="info-value">{basicInfo.Name}</span>
            </div>

            <div className="info-item">
              <span className="info-label">Introduction：</span>
              <span className="info-value">{basicInfo.Introduction}</span>
            </div>

            <div className="info-item">
              <span className="info-label">Superclass：</span>
              <span className="info-value">{basicInfo.Superclass}</span>
            </div>

            <div className="info-item">
              <span className="info-label">Class：</span>
              <span className="info-value">{basicInfo.Class}</span>
            </div>
          </div>
        </div>

        {/* Source */}
        <div className="info-section">
          <div className="section-header">
            <div className="section-icon">
              <img src={SourceIcon} alt="node" width={16} height={16} />
            </div>
            <span className="section-title">Source</span>
          </div>

          <div className="info-items">
            <div className="info-item">
              <span className="info-label">Title：</span>
              <span className="info-value">{source?.Title}</span>
            </div>

            <div className="info-item">
              <span className="info-label">Author：</span>
              <span className="info-value">{source?.Author}</span>
            </div>

            <div className="info-item">
              <span className="info-label">Doi：</span>
              <span className="info-value">{source?.Doi}</span>
            </div>

            <div className="info-item">
              <span className="info-label">Journal：</span>
              <span className="info-value">{source?.Journal}</span>
            </div>

            <div className="info-item">
              <span className="info-label">Year：</span>
              <span className="info-value">{source?.Year}</span>
            </div>

            <div className="info-item">
              <span className="info-label">PMID：</span>
              <span className="info-value">{source?.PMID}</span>
            </div>

            <div className="info-item">
              <span className="info-label">URL：</span>
              <a
                href={source?.URI}
                target="_blank"
                rel="noopener noreferrer"
                className="info-value info-link"
              >
                {source?.URI}
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="right-panel">
        {/* 控制区域 */}
        <div className="controls-section">
          <div className="controls-row">
            <Select value={unit} onChange={setUnit} className="unit-select" placeholder="UNIT: all">
              <Option value="all">all</Option>
              <Option value="kg">kg</Option>
              <Option value="lb">lb</Option>
            </Select>

            <Select
              value={sampleNumber}
              onChange={setSampleNumber}
              className="sample-select"
              placeholder="Sample number: all"
            >
              <Option value="all">all</Option>
              <Option value="10">10</Option>
              <Option value="20">20</Option>
            </Select>

            <Button type="primary" className="search-button">
              Search
            </Button>
          </div>
        </div>

        {/* 数据表格 */}
        <div className="data-table-section">
          <Table
            // columns={mockColumns}
            // dataSource={mockTableData}
            columns={convertToColumns(tableColumns)}
            dataSource={tableData}
            // loading={isLoading}
            pagination={false}
            scroll={{ x: 'max-content' }}
            size="small"
            bordered
            className="dataset-table"
          />
        </div>
      </div>
    </div>
  )
}
