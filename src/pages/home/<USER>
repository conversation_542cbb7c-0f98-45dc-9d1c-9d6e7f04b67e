/* 默认：允许滚动（适用于 <1320px） */
html,
body {
    margin: 0;
    padding: 0;
    max-width: 100vw;
}

/* 仅在 ≥1320px 时隐藏横向滚动 */
@media (min-width: 1320px) {

    html,
    body {
        overflow-x: hidden;
    }
}

.tableStyle .ant-tabs-nav::before {

    border-bottom: 0 !important
}

.ant-tabs-nav .ant-tabs-nav-wrap {
    overflow: visible !important
}

.body-new-content {
    position: relative;
    z-index: 2;
}

.body-new-content-title {
    position: relative;
    z-index: 3;
}

.body-new {
    max-height: 827px;
    position: absolute;
    left: 50%;
    top: -17%;
    transform: translateX(-50%);
    z-index: -1;
}

.box-shadow-img {
    /* box-shadow: -15px 0 30px -10px rgb(242, 255, 94, 0.3),
        15px 0 30px -10px rgba(68, 132, 255, 0.3); */

}


.card-active-gradient {
    background: linear-gradient(269.79deg, #FFFFFF 0.13%, #F9FAFF 99.78%);
    border: 2px solid #F2F4F7;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ant-tabs .ant-tabs-tab:hover {
    color: #000000D9 !important
}

.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #000000D9;
    text-shadow: none;
}

.ant-tabs .ant-tabs-tab {
    padding: 0
}

.ant-tabs-top>.ant-tabs-nav {
    margin: 0;
}

.ant-tabs .ant-tabs-tab.ant-tabs-tab-focus .ant-tabs-tab-btn {
    outline: none
}