import clsx from 'clsx'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import './index.css'
import { Tabs } from 'antd'
import BodyNew from '@/assets/BodyNew.svg'
import AllinOne from '@/assets/AllinOne.svg'
import Risk from '@/assets/Risk.svg'
import Powered from '@/assets/Powered.svg'
import Multi from '@/assets/Multi.svg'
import DataTypeCard from '@/assets/Data-Type-Card.svg'
import backgroundSecond from '@/assets/backgroundSecond.png'

import Differentinal from '@/assets/differentinal.svg'
import Phenotype from '@/assets/phenotype.svg'
import Pathway from '@/assets/pathway.svg'
import Co from '@/assets/co.svg'
import Prediction from '@/assets/prediction.svg'
import ToolsItem from '@/assets/ToolsItem.png'
import Tools from '@/assets/Tools.png'
import Background from '@/assets/background.png'

export default function HomePage() {
  const [activeIndex, setActiveIndex] = useState(0)
  const [activeKey, setActiveKey] = useState('1')
  const navigate = useNavigate()
  const { TabPane } = Tabs

  const items = [
    {
      icon: Differentinal,
      title: 'Differential analysis',
      description:
        'Differential analysis involves comparing group differences to find statistically significant features using tests like t-test, ANOVA, or machine learning. Outputs such as fold-change, adjusted p-values, and effect sizes are visualized via volcano plots, heatmaps, etc.',
    },
    {
      icon: Phenotype,
      title: 'Phenotype-omics association analysis',
      description:
        'Combine phenotype data with omics data to uncover the genetic basis behind plateau adaptation traits, driving the development of precision medicine and personalized health management.',
    },
    {
      icon: Pathway,
      title: 'Coexpression network',
      description:
        'Through the construction of gene co-expression network, the interaction between genes in the process of plateau adaptation was explored, and the gene regulation mechanism and biological process were deeply understood.',
    },
    {
      icon: Co,
      title: 'Path enrichment analysis',
      description:
        'The enrichment of genes in biological pathways was analyzed to reveal the key molecular mechanisms in the process of altitude adaptation, providing theoretical support for drug development and treatment strategies.',
    },
    {
      icon: Prediction,
      title: 'Plateau prediction model',
      description:
        'Based on big data and machine learning methods, the prediction model of acute and chronic altitude sickness was established to help identify health risks in the altitude environment and provide basis for early intervention.',
    },
  ]
  return (
    <div className="flex flex-col w-[100%] items-center min-w-[1320px] flex-1">
      {/*main部分 */}
      <div
        className="flex flex-col items-center w-[100%] h-[870px]"
        style={{
          backgroundImage: `url(${Background})`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'top center',
          backgroundSize: '100% 960px',
          paddingBottom: '164px',
        }}
      >
        {/*High-Altitud Acclimatization部分 */}
        <div className="flex flex-col gap-4 items-center pt-[60px] pb-[60px]">
          <div className="text-4xl text-black/85 font-medium text-[52px] leading-[62px] text-center tracking-normal">
            High-Altitud Acclimatization
          </div>
          <div className="text-2xl text-white/65 font-normal text-[20px] leading-[26px] text-center tracking-normal">
            Understanding how the human body acclimatize to high-altitude environments
          </div>
        </div>
        <div className="max-w-6xl text-black/85 text-[20px] font-medium">
          When people who have lived in the plains enter the plateau or when people who have lived
          in the plateau enter higher altitude areas, the human body will undergo a series of
          complex physiological changes under the stimulation of the special environment of the
          plateau, which is called high-altitude acclimatization. High-altitude acclimatization
          includes two main stages, namely the acute stage of plateau acclimatization (usually
          within 3 days after entering the plateau) and the chronic stage of plateau acclimatization
          (usually more than 30 days after moving to the plateau).
        </div>
        <div className="flex max-w-6xl gap-12 mt-[40px]">
          <div className="w-[556px] h-auto flex flex-col  p-6 rounded-2xl  bg-gradient-to-b from-white/50 to-[#E2F1FF]/60 backdrop-blur-[4px] border-white border-1">
            <div className="text-2xl text-[20px] text-[#003C97]">
              High-Altitude Risks: Acclimatization Research
            </div>
            <div className="mt-3 text-[18px] text-black/65 font-normal tracking-normal leading-[26px]">
              Acute hypoxia may trigger life-threatening HAPE or HACE, while chronic exposure
              induces erythrocytosis, pulmonary hypertension, and cardiac dysfunction. Here, we
              establish an open-access platform to advance research and collaboration in
              high-altitude acclimatization, addressing the health impacts of acute/chronic mountain
              sickness on high-altitude populations.
            </div>
          </div>
          {/* break-words*/}
          <div className="w-[556px] h-auto flex flex-col  p-6 rounded-2xl  bg-gradient-to-b from-white/50 to-[#E2F1FF]/60 backdrop-blur-[4px] border-white border-1">
            <div className="text-2xl text-[20px] text-[#003C97]">
              High-Altitude Acclimatization Phenotypic Change
            </div>
            <div className="mt-3 text-[18px] text-black/65 font-normal tracking-normal leading-[26px]">
              During the process of acclimatization to high-altitude environments, the human body
              undergoes rapid and sustained changes in both macro- and micro-level phenotypic
              characteristics, including increased breathing rate, higher heart rate, and changes in
              blood composition. These adjustments help compensate for the reduced oxygen
              availability at higher altitudes and maintain essential body functions.
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col w-full gap-[57px] mt-[60px] items-center ">
        {' '}
        {/*EFFICIENT RETRIEVAL部分 */}
        <div className="text-2xl text-[32px] flex justify-center">EFFICIENT RETRIEVAL</div>
        <Tabs
          className="tableStyle flex "
          defaultActiveKey="1"
          centered
          tabBarGutter={350}
          indicator={{ size: () => 0 }}
          onChange={key => setActiveKey(key)}
          style={{
            width: '100%',
            maxWidth: '1136px',
          }}
        >
          <TabPane
            tab={
              <div className="relative h-[68px] flex flex-col">
                <div className="h-[34px] flex items-center justify-center ">
                  <span
                    className={clsx(
                      'font-sf-pro font-[510] text-[32px]',
                      'relative z-10',
                      activeKey === '1' && [
                        'text-transparent bg-clip-text',
                        'bg-gradient-to-r from-[#18181B] to-[#1975FF] ',
                      ],
                    )}
                  >
                    Graphical search
                  </span>
                </div>

                <div className="h-[34px] relative ">
                  {activeKey === '1' && (
                    <div
                      className="absolute right-0 h-[10px] w-[224px] rounded-[12px] 
                         bg-gradient-to-r from-[#F3F7FF] to-[#1975FF]"
                      style={{ top: '24px' }}
                    />
                  )}
                </div>
              </div>
            }
            key="1"
          >
            <div className="body-new-content-title w-full mx-auto font-normal not-italic text-[24px] tracking-normal mt-[50px]">
              Through an interactive human anatomical diagram, users can select specific organs
              (such as the heart or lungs) to view associated phenotypic data, while enabling direct
              clicks on linked data markers for interactive navigation.
            </div>
            <div className="body-new-content flex justify-center items-center w-full h-[714px] mb-[50px]">
              <img
                src={BodyNew}
                className=" body-new top-[-20px] cursor-pointer"
                alt="Body Diagram"
                onClick={() => navigate('/dataset')}
              />
            </div>
          </TabPane>
          <TabPane
            tab={
              <div className="relative h-[68px] flex flex-col">
                <div className="h-[34px] flex items-center justify-center">
                  <span
                    className={clsx(
                      'font-sf-pro font-[510] text-[32px]',
                      activeKey === '2' && [
                        'text-transparent bg-clip-text',
                        'bg-gradient-to-r from-[#18181B] to-[#1975FF]',
                      ],
                    )}
                  >
                    Data-Type-Based Search
                  </span>
                </div>

                <div className="h-[34px] relative">
                  {activeKey === '2' && (
                    <div
                      className="absolute right-0 h-[10px] w-[291px] rounded-[12px] 
                         bg-gradient-to-r from-[#F3F7FF] to-[#1975FF]"
                      style={{ top: '24px' }}
                    />
                  )}
                </div>
              </div>
            }
            key="2"
          >
            <div className="w-full mx-auto font-normal not-italic text-[24px] tracking-normal mt-[50px]">
              This interface allows users to intuitively explore phenotypic data by selecting data
              types (e.g., Methylation Information, Metabolism Information) and clicking directly on
              entries to view detailed records, annotations, or linked datasets.
            </div>
            <div
              className="flex flex-1 flex-col gap-4 px-30 w-full h-full items-center justify-center bg-no-repeat bg-center bg-contain mt-[73px] mb-[104px] cursor-pointer"
              style={{
                backgroundImage: `url(${DataTypeCard}) `,
                backgroundSize: 'contain',
                height: '529px',
                filter: `
        drop-shadow(0 -15px 10px rgba(79, 149, 255, 0.1)) 
        drop-shadow(0 15px 10px rgba(79, 149, 255, 0.1))  
      `,
              }}
            ></div>
          </TabPane>
        </Tabs>
      </div>
      <div className="flex items-center justify-center flex-col bg-[#FAFAFA] w-full">
        {/*Product advantage */}
        <div className="mt-[60px]">
          <div className="text-[32px] font-510 leading-[100%] tracking-normal text-black/85 w-full flex align-middle justify-center">
            PRODUCT ADVANTAGE
          </div>
          <div className="flex items-center justify-center mt-[50px] mb-[60px] w-6xl">
            <div className="flex flex-1/4 items-center justify-center flex-col">
              <div
                className="w-[150px] h-[150px]"
                style={{ backgroundImage: `url(${AllinOne})` }}
              ></div>
              <div className="mt-4  flex text-2xl align-middle justify-center">All-in-one</div>
            </div>
            <div className="flex flex-1/4 items-center justify-center flex-col">
              <div
                className="w-[150px] h-[150px] items-center"
                style={{ backgroundImage: `url(${Risk})` }}
              ></div>
              <div className="mt-4  flex text-2xl align-middle justify-center">Risk assessment</div>
            </div>
            <div className="flex flex-1/4 items-center justify-center flex-col">
              <div
                className="w-[150px] h-[150px]"
                style={{ backgroundImage: `url(${Multi})` }}
              ></div>
              <div className="mt-4  flex text-2xl align-middle justify-center">Multi-analysis</div>
            </div>
            <div className="flex flex-1/4 items-center justify-center flex-col">
              <div
                className="w-[150px] h-[150px]"
                style={{ backgroundImage: `url(${Powered})` }}
              ></div>
              <div className="mt-4  flex text-2xl align-middle justify-center">AI-powered</div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="flex flex-col w-full h-[auto] gap-6 mt-[60px] pb-[182px]"
        style={{ backgroundImage: `url(${backgroundSecond})` }}
      >
        {/*Five analytical tools部分 */}
        <h2 className="font-sf-pro font-medium text-[32px] leading-[100%] tracking-[0%] text-center uppercase">
          Five analytical tools
        </h2>
        <div className="flex h-[auto] mt-[60px]">
          {/* <div className="w-1/2 bg-white flex items-center justify-center p-4">
            <div className="relative flex items-center justify-center">
              <img
                src={Tools}
                alt="Analytical Tools"
                className="max-h-[150%] max-w-[150%] object-contain"
              />
              <img
                src={ToolsItem}
                alt="Overlay Icon"
                className="absolute box-shadow-img   max-h-[150%] max-w-[150%] object-contain top-25 left-20 "
                style={{
                  background: 'linear-gradient(90deg, #4484FF -0.55%, #44B0FF 22.86%, #FF44EC 48.36%, #44A5FF 73.33%, #F2FF5E 99.34%)',
                  margin: '-6px'
                }}
              />
            </div>
          </div> */}
          <div className="w-1/2 bg-white flex items-center justify-center p-4  h-[504px]">
            <div className="relative flex items-center justify-center ">
              <img
                src={Tools}
                alt="Analytical Tools"
                className="max-h-[150%] max-w-[150%] object-contain z-10"
              />
              <div className="absolute top-25 left-20 z-20">
                <div className="relative">
                  <div
                    className="absolute -inset-0 blur-lg opacity-30 -z-10 rounded-none"
                    style={{
                      background:
                        'linear-gradient(-90deg, #4484FF -0.55%, #44B0FF 22.86%, #FF44EC 48.36%, #44A5FF 73.33%, #F2FF5E 99.34%)',
                    }}
                  />
                  <img
                    src={ToolsItem}
                    alt="Overlay Icon"
                    className="max-h-[150%] max-w-[150%] object-contain relative border-2 border-white/50 "
                    style={{
                      boxShadow: 'inset 0 0 0 1px rgba(255,255,255,0.3)',
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <ul className="w-1/2 space-y-8 pl-4">
            {' '}
            {/* pl-4左侧间距 */}
            {items.map((item, index) => (
              <li
                key={index}
                className={clsx(
                  'transition-all duration-300 p-6 shadow-[0_0_10px_0_rgba(0,0,0,0.06)]',
                  'border-2 border-transparent rounded-lg',
                  'w-full max-w-[601px]',
                  'transition-delay-300', // 延迟
                  activeIndex === index
                    ? [
                        'h-[auto]',
                        'border-2 border-[#F2F4F7] rounded-lg',
                        'px-6',
                        'card-active-gradient',
                      ]
                    : 'hover:bg-gray-50 cursor-pointer',
                )}
                // onMouseEnter={() => setActiveIndex(index)}
                onMouseEnter={() => {
                  setTimeout(() => {
                    setActiveIndex(index)
                  }, 300)
                }}
                // onMouseLeave={() => {
                //   clearTimeout();
                //   setActiveIndex(-1);
                // }}
              >
                <h3
                  className={clsx(
                    'flex font-medium',
                    activeIndex === index ? 'text-blue-500' : 'text-gray-800', // 添加颜色变化
                  )}
                >
                  <img src={item.icon} alt="icon" className="w-6 h-6 mr-2" />
                  {item.title}
                </h3>
                {activeIndex === index && (
                  <p className="mt-2 text-sm text-gray-600">{item.description}</p>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}
