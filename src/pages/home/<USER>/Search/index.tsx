import React, { useState, useMemo, useEffect } from 'react';
import { Tree, Card, Table, Input, Button, Select } from 'antd';
import type { GetProps, TreeDataNode } from 'antd';
import InputSearch from "@/assets/search-input.svg";
import NoContent from "@/assets/Nocontent.svg";
import "./index.css";
import { useI18n } from '@/hooks/useI18n';
import { useSearchData, useSearchTableData } from "@/hooks/useUserQuery";
import { useDataPreview } from '@/hooks/useSearchQuery'
import { DataNode } from 'antd/es/tree';
import { CloseOutlined, FolderOpenOutlined } from '@ant-design/icons';
import { convertToColumns } from '@/lib/utils.ts'

type DirectoryTreeProps = GetProps<typeof Tree.DirectoryTree>;

const { DirectoryTree } = Tree;
const { Option } = Select;

interface CustomDataNode extends DataNode {
    key?: string;
    type?: string;
    path?: string;
}

interface ClassificationNode {
    id: string;
    name: string;
    isFolder: boolean;
    sequence: number;
    type?: string;
    path?: string;
    children?: ClassificationNode[];
    details?: any;
}

const coverData = (apiData: ClassificationNode[]): CustomDataNode[] => {
    if (!apiData || !apiData.length) return [];

    return apiData
        .filter(item => item.isFolder)
        .map(item => {
            const hasChildren = item.children?.some(child => child.isFolder);
            const treeNode: TreeDataNode = {
                key: item.id,
                title: item.name,
                isLeaf: !hasChildren,
                icon: (
                    <span
                        className={hasChildren ? 'parent-node' : 'child-node'}
                    >
                        <FolderOpenOutlined />
                    </span>
                )
            };

            if (hasChildren) {
                treeNode.children = coverData(item.children!);
            }
            return treeNode;
        });
};

const filterTreeData = (data: CustomDataNode[], searchText: string): CustomDataNode[] => {
    if (!searchText) return data;

    const lowerSearchText = searchText.toLowerCase();

    return data
        .filter(item => {
            const nodeMatch = item.title.toLowerCase().includes(lowerSearchText);
            const childrenMatch = item.children
                ? filterTreeData(item.children, searchText).length > 0
                : false;
            return nodeMatch || childrenMatch;
        })
        .map(item => ({
            ...item,
            children: item.children ? filterTreeData(item.children, searchText) : undefined
        }));
};

const items = [
    { label: 'all', value: 'all' },
    { label: 'option1', value: 'option1' },
    { label: 'option2', value: 'option2' },
    { label: 'option3', value: 'option3' },
];

const App: React.FC = () => {
    const [classificationSelectedKeys, setClassificationSelectedKeys] = useState<React.Key[]>([]);
    const [fileSelectedKeys, setFileSelectedKeys] = useState<React.Key[]>([]);
    const [currentTableData, setCurrentTableData] = useState<any[]>([]);
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [tableLoading, setTableLoading] = useState(false);
    const [tableColumns, setTableColumns] = useState([]);
    const [classificationSearchText, setClassificationSearchText] = useState('');
    const [fileSearchText, setFileSearchText] = useState('');
    const [selectedFileInfo, setSelectedFileInfo] = useState<{ path?: string; type?: string; key?: string }>({});
    const { t } = useI18n();

    const { data: searchData, isLoading } = useSearchData();

    // 使用 selectedFileInfo 作为参数获取表格数据
    const { data: previewData, isLoading: previewLoading } = useDataPreview({
        params: {
            module: 'search',
            id: selectedFileInfo.key,
            all: 0
        },
        enabled: !!selectedFileInfo.path // 只有 path 存在时才启用查询
    });
    console.log("previewData", previewData)
    const originalClassificationData = useMemo(() => coverData(searchData || []), [searchData]);

    const classificationTreeData = useMemo(() => {
        return filterTreeData(originalClassificationData, classificationSearchText);
    }, [originalClassificationData, classificationSearchText]);

    const selectedCategoryKey = classificationSelectedKeys.length > 0
        ? classificationSelectedKeys[0]
        : null;

    // 修改后的文件选择处理函数
    const onFileSelect: DirectoryTreeProps['onSelect'] = (keys, info) => {
        if (info.selected) {
            setFileSelectedKeys(keys);
            if (info.node) {
                // 从节点数据中获取 path 和 type
                const { path, type, key } = info.node as CustomDataNode;
                setSelectedFileInfo({ path, type, key });
            }
        }
    };

    // 监听预览数据变化，更新表格
    useEffect(() => {
        if (previewData) {
            // 调试：打印原始数据结构
            console.log("原始数据预览:", {
                header: previewData.header,
                firstDataRow: previewData.data[0]
            });

            // 1. 生成列配置（确保header是数组）
            // const columns = Array.isArray(previewData.header)
            //     ? previewData.header.map((h: any) => ({
            //         title: h.title || h.fieldName || h, // 兼容字符串或对象
            //         dataIndex: h.fieldName || h,       // 使用fieldName或直接使用字符串
            //         key: h.fieldName || h,
            //         width: 150 // 可选列宽
            //     }))
            //     : []; // 如果header不是数组，返回空列

            // 2. 转换数据行
            // const data = previewData.data.map((row: any, rowIndex: number) => {
            //     const transformedRow: any = { key: `row-${rowIndex}` };
            //
            //     // 根据header定义提取数据
            //     previewData.header.forEach((header: any, colIndex: number) => {
            //         const fieldName = header.fieldName || header; // 获取字段名
            //         transformedRow[fieldName] = row[colIndex] ?? null; // 使用数字索引获取数据
            //     });
            //
            //     return transformedRow;
            // });

          // console.log("转换后的数据示例:", data[0]);

            const columns = convertToColumns(previewData.header);
            console.log("生成的列配置:", columns);

            setTableColumns(columns);
            setCurrentTableData(previewData.data || []);
        } else {
            setTableColumns([]);
            setCurrentTableData([]);
        }
    }, [previewData]);



    const generateFileTreeData = useMemo(() => {
        if (!selectedCategoryKey || !searchData) return [];

        const findAllFiles = (data: ClassificationNode[]): CustomDataNode[] => {
            return data.reduce((acc: CustomDataNode[], item) => {
                if (!item.isFolder) {
                    acc.push({
                        key: item.id,
                        title: item.name,
                        isLeaf: true,
                        type: item.type,  // 确保 type 被传递
                        path: item.path   // 确保 path 被传递
                    });
                } else if (item.children) {
                    acc.push(...findAllFiles(item.children));
                }
                return acc;
            }, []);
        };

        const findFolder = (data: ClassificationNode[], key: string): ClassificationNode | null => {
            for (const item of data) {
                if (item.id === key) return item;
                if (item.children) {
                    const found = findFolder(item.children, key);
                    if (found) return found;
                }
            }
            return null;
        };

        const selectedFolder = findFolder(searchData, selectedCategoryKey as string);
        const files = selectedFolder ? findAllFiles([selectedFolder]) : [];

        // return [{
        //     key: 'allFiles',
        //     title: (
        //         <span className="parent-node">
        //             All
        //         </span>
        //     ),
        //     isLeaf: false,
        //     children: files
        // }];
        return [{
            key: 'allFiles',
            title: "All",
            isLeaf: false,
            selectable: false,  // 仅禁用All节点的选中
            children: files     // 子文件保持可选中
        }];
    }, [selectedCategoryKey, searchData]);

    const fileTreeData = useMemo(() => {
        if (!fileSearchText || !generateFileTreeData.length) return generateFileTreeData;

        const lowerSearchText = fileSearchText.toLowerCase();

        // 保持原结构，只过滤children
        return generateFileTreeData.map(rootNode => ({
            ...rootNode,
            children: rootNode.children?.filter(item =>
                item.title.toLowerCase().includes(lowerSearchText)
            )
        }));
    }, [generateFileTreeData, fileSearchText]);

    const onClassificationSelect: DirectoryTreeProps['onSelect'] = (keys, info) => {
        if (info.selected) {
            setClassificationSelectedKeys(keys);
            setFileSelectedKeys([]);
            setCurrentTableData([]);
            setTableColumns([]);
            setSelectedFileInfo({}); // 清空选中的文件信息
        }
    };

    const getDefaultExpandedKeys = (data: ClassificationNode[]): string[] => {
        return data.reduce((keys: string[], item) => {
            if (item.isFolder && item.children?.some(child => child.isFolder)) {
                keys.push(item.id);
                if (item.children) {
                    keys.push(...getDefaultExpandedKeys(item.children));
                }
            }
            return keys;
        }, []);
    };

    useEffect(() => {
        if (searchData) {
            setExpandedKeys(getDefaultExpandedKeys(searchData));
        }
    }, [searchData]);

    const onExpand: DirectoryTreeProps['onExpand'] = (keys, info) => {
        setExpandedKeys(keys);
    };
    useEffect(() => {
        console.log("当前列配置:", tableColumns);
        console.log("当前表格数据:", currentTableData);
    }, [tableColumns, currentTableData]);

    return (
        <div className="flex flex-col min-h-screen w-full max-w-screen-2xl mx-auto pt-[48px] pl-[48px] pr-[48px]">
            {/* 顶部双卡片布局 */}
            <div className="flex w-full max-w-full gap-6" style={{ minHeight: '612px' }}>
                {/* 左侧目录分类卡片 */}
                <Card
                    title={t('search.title1')}
                    className="flex-1 card-style lex flex-col card-style"
                    headStyle={{
                        fontFamily: 'PingFang SC, sans-serif',
                        fontWeight: 600,
                        fontSize: '20px',
                        lineHeight: '24px',
                        color: '#000000D9',
                        verticalAlign: 'middle',
                        padding: '24px 24px 12px 24px',
                        borderBottom: 'none'
                    }}
                    bodyStyle={{
                        padding: '12px 24px 24px 24px',
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column'
                    }}
                >
                    <div className="mb-4">
                        <Input
                            size="large"
                            placeholder={t('search.searchInput')}
                            prefix={<img src={InputSearch} />}
                            value={classificationSearchText}
                            onChange={(e) => setClassificationSearchText(e.target.value)}
                            suffix={classificationSearchText && (
                                <CloseOutlined
                                    onClick={() => {
                                        setClassificationSearchText('');
                                        setExpandedKeys(getDefaultExpandedKeys(searchData || []));
                                    }}
                                    style={{ color: 'rgba(0,0,0,.45)', cursor: 'pointer' }}
                                />
                            )}
                            className='input-style'
                        />
                    </div>
                    {classificationTreeData.length > 0 ? (
                        <DirectoryTree
                            showIcon
                            titleRender={(node) => (
                                <span
                                    className={node.isLeaf ? 'child-node' : 'parent-node'}
                                    onClick={(e) => {
                                        e.stopPropagation();  // 阻止事件冒泡，避免触发展开/折叠
                                        // 手动触发选中
                                        onClassificationSelect([node.key], {
                                            node,
                                            selected: true,
                                            selectedNodes: [node]
                                        });
                                    }}
                                >
                                    {node.title}
                                </span>
                            )}
                            expandedKeys={expandedKeys}
                            onSelect={onClassificationSelect}
                            onExpand={onExpand}
                            treeData={classificationTreeData}
                            className="text-base custom-tree"
                            selectedKeys={classificationSelectedKeys}
                            motion={{
                                motionDeadline: 0
                            }}

                            style={{
                                height: "468px",
                                overflowY: "auto"
                            }}
                        />
                    ) : (
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            flex: 1,
                            minHeight: '400px'
                        }}>
                            <img src={NoContent} alt="No content" />
                        </div>
                    )}
                </Card>

                {/* 右侧数据文件卡片 */}
                <Card
                    title={t('search.title2')}
                    className="flex-1 h-[612px] card-style"
                    headStyle={{
                        fontFamily: 'PingFang SC, sans-serif',
                        fontWeight: 600,
                        fontSize: '20px',
                        lineHeight: '24px',
                        color: '#000000D9',
                        verticalAlign: 'middle',
                        padding: '24px 24px 12px 24px',
                        borderBottom: 'none'
                    }}
                    bodyStyle={{
                        padding: '12px 24px 24px 24px',
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column'
                    }}
                >
                    <div className="mb-4">
                        <Input
                            size="large"
                            placeholder={t('search.searchInput')}
                            prefix={<img src={InputSearch} />}
                            value={fileSearchText}
                            onChange={(e) => setFileSearchText(e.target.value)}
                            suffix={fileSearchText && (
                                <CloseOutlined
                                    onClick={() => setFileSearchText('')}
                                    style={{ color: 'rgba(0,0,0,.45)', cursor: 'pointer' }}
                                />
                            )}
                            className='input-style'
                        />
                    </div>
                    {fileTreeData[0]?.children?.length > 0 ? (
                        <DirectoryTree
                            showIcon
                            titleRender={(node) => {
                                // 特别处理All节点
                                if (node.key === 'allFiles') {
                                    return (
                                        <span
                                            className="parent-node"
                                            onClick={(e) => e.stopPropagation()} // 阻止文字点击事件
                                        >
                                            {node.title}
                                        </span>
                                    );
                                }
                                // 其他文件节点保持原有选中效果
                                return (
                                    <span
                                        className={node.isLeaf ? 'child-node' : 'parent-node'}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onFileSelect([node.key], {
                                                node,
                                                selected: true,
                                                selectedNodes: [node]
                                            });
                                        }}
                                    >
                                        {node.title}
                                    </span>
                                );
                            }}
                            defaultExpandAll
                            onSelect={onFileSelect}
                            treeData={fileTreeData}
                            className="text-base custom-tree"
                            selectedKeys={fileSelectedKeys}
                            motion={{
                                motionDeadline: 0
                            }}

                            style={{
                                height: "468px",
                                overflowY: "auto"
                            }}
                        />
                    ) : (
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            flex: 1,
                            minHeight: '400px'
                        }}>
                            <img src={NoContent} alt="No content" />
                        </div>
                    )}
                </Card>
            </div>

            {/* 底部数据概览卡片 */}
            <div className="flex-1 w-full h-[auto] mt-[24px]">
                <Card
                    title={t('search.title3')}
                    className="w-full h-full flex flex-col"
                    headStyle={{
                        fontWeight: 'bold',
                        fontSize: '16px',
                        padding: '24px 0px 20px 24px',
                        borderBottom: 'none'
                    }}
                    bodyStyle={{
                        padding: '0px 24px 24px 24px',
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column'
                    }}
                    style={{
                        overflowX: "auto"
                    }}
                >
                    <div className="flex flex-wrap items-center gap-4 mb-4">
                        <div className="flex items-center flex-wrap gap-4">
                            <Select defaultValue="all" style={{ width: 150, marginRight: 16 }}>
                                {items.map(item => (
                                    <Option key={item.value} value={item.value}>{item.label}</Option>
                                ))}
                            </Select>
                            <Select defaultValue="all" style={{ width: 150, marginRight: 16 }}>
                                {items.map(item => (
                                    <Option key={item.value} value={item.value}>{item.label}</Option>
                                ))}
                            </Select>
                            <Button type="primary">Search</Button>
                        </div>
                    </div>
                    <div className='flex-1 h-[329px] overflow-x-auto overflow-y-auto max-w-full' style={{ height: '400px', minHeight: '400px' }}>
                        <Table
                            className='table-style'
                            columns={tableColumns}
                            dataSource={currentTableData}
                            loading={tableLoading || previewLoading}
                            size="middle"
                            bordered
                            sticky
                            pagination={false}
                            scroll={{ y: 'calc(400px - 60px)' }} // 减去表头和其他元素的高度
                            locale={{
                                emptyText: fileSelectedKeys.length === 0
                                    ? (
                                        <div style={{ textAlign: 'center', padding: '40px 0' }}>
                                            <img src={NoContent} style={{ margin: '0 auto' }} alt="No file selected" />
                                        </div>
                                    )
                                    : (
                                        <div style={{ textAlign: 'center', padding: '40px 0' }}>
                                            <img src={NoContent} style={{ margin: '0 auto' }} alt="No data available" />
                                        </div>
                                    )
                            }}
                        />
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default App;