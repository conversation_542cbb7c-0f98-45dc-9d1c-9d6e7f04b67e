/* html,
body {
  margin: 0 !important;
  padding: 0 !important;
  width: 100%;
  overflow-x: auto; 
} */
/* 控制全局滚动行为 */
body {
  overflow-x: hidden; /* 始终隐藏横向滚动 */
}
 
@media (max-width: 1319px) {
  body {
    overflow-x: auto; /* 小于1320px时启用 */
  }
}
/* 表格容器特殊处理 */
.table-container {
  mask-image: linear-gradient(
    to right,
    transparent,
    black 20px,
    black calc(100% - 20px),
    transparent
  ); /* 边缘渐变效果 */
}
.ant-input-affix-wrapper .ant-input-prefix {
    margin-inline-end: 8px
}

.ant-tree {
    background: transparent;

}

.ant-tree .ant-tree-treenode {
    display: inline-block
}

/* .custom-tree{
     display: inline-block
} */
.custom-tree.ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper {
    color: #1975FF;
    /* font-weight: 510; */

}

.custom-tree.ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper:before {
    background: transparent;
}

.ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher {
    color: #000000D9;
}

.custom-tree .ant-tree-node-content-wrapper {
    display: inline-block;
    padding: 0 4px;
    border-radius: 2px;
}
.card-style {
    width: 660px;
    height: 612px;
    background: linear-gradient(180deg, #ECF5FF 0%, #FFFFFF 80.14%);
    border: 1px solid #EDF1F7;
    border-radius: 5px;
    padding: 0;
    overflow: hidden;
}

.input-style {
    width: 100%;
    height: 32px;
    opacity: 1;
    border-radius: 2px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
}

.ant-tree .ant-tree-switcher:not(.ant-tree-switcher-noop):hover:before {
    background-color: transparent;
}

/* .table-style.ant-table-wrapper .ant-table {
  border-radius: 0 !important;
} */
/* 固定缩进单位宽度 */
/* .ant-tree-indent-unit {
  width: 24px;
  flex-shrink: 0; 
} */
.ant-tree .ant-tree-switcher .ant-tree-switcher-icon {
    display: inline-flex
}

.parent-node {
    font-weight: 510;
    color: #000000D9;
}

.child-node {
    font-weight: 400;
    color: #000000A6;
}

.custom-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: #4D6EF01A !important;

    & .parent-node {
        color: #1975FF !important;
    }

    & .child-node {
        color: #1975FF  !important;
         font-weight: 400;
    }
}
