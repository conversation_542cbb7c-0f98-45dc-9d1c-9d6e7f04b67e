import React, { useState } from 'react'
import DataPreparation from './tool/components/data-preparation'
import { message } from 'antd'

export default function DataPrepDemo() {
  const [currentStep, setCurrentStep] = useState(2)

  const handlePrevious = () => {
    message.info('Previous step clicked')
    setCurrentStep(prev => Math.max(1, prev - 1))
  }

  const handleNext = () => {
    message.success('Next step clicked')
    setCurrentStep(prev => Math.min(5, prev + 1))
  }

  return (
    <div
      style={{
        padding: '48px',
        backgroundColor: '#f5f5f5',
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'flex-start',
      }}
    >
      <div
        style={{
          backgroundColor: 'white',
          padding: '48px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          width: '100%',
          maxWidth: '900px',
        }}
      >
        <div
          style={{
            marginBottom: '32px',
            textAlign: 'center',
            color: 'rgba(0,0,0,0.85)',
          }}
        >
          <h1 style={{ fontSize: '24px', fontWeight: 500, margin: 0 }}>
            Data Preparation - Step {currentStep}
          </h1>
          <p
            style={{
              fontSize: '14px',
              color: 'rgba(0,0,0,0.45)',
              margin: '8px 0 0 0',
            }}
          >
            Click Previous/Next buttons to test navigation
          </p>
        </div>

        <DataPreparation onPrevious={handlePrevious} onNext={handleNext} />

        <div
          style={{
            marginTop: '32px',
            padding: '16px',
            backgroundColor: '#f8f9fa',
            borderRadius: '6px',
            fontSize: '14px',
            color: 'rgba(0,0,0,0.65)',
          }}
        >
          <h4 style={{ margin: '0 0 8px 0', color: 'rgba(0,0,0,0.85)' }}>Features Implemented:</h4>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>✅ Ant Design Select components with proper styling</li>
            <li>✅ File list hover effects matching design</li>
            <li>✅ English button labels (Previous/Next)</li>
            <li>✅ Step navigation callbacks</li>
            <li>✅ Responsive design</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
