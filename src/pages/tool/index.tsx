import { useState } from 'react'
import { Steps } from 'antd'
import './index.css'
import Introduction from './components/introduction/index'
import DataPreparation from './components/data-preparation/index'
import ReportDownload from './components/report-download/index'

export default function ToolPage() {
  const [currentStep, setCurrentStep] = useState(0)

  const handleUploadClick = () => {
    setCurrentStep(1)
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(0, prev - 1))
  }

  const handleNext = () => {
    setCurrentStep(prev => Math.min(2, prev + 1))
  }

  const steps = [
    { title: 'introduction', component: () => <Introduction onUploadClick={handleUploadClick} /> },
    {
      title: 'Data Preparation',
      component: () => <DataPreparation onPrevious={handlePrevious} onNext={handleNext} />,
    },
    {
      title: 'Report Download',
      component: () => <ReportDownload onPrevious={handlePrevious} onNext={handleNext} />,
    },
  ]

  const StepComponent = steps[currentStep].component

  return (
    <div className="tool-page">
      {/* 蓝色渐变背景区域 */}
      <div className="hero-section">
        <h1 className="hero-title">Differential Expression Analysis</h1>
        <p className="hero-description">
          Introduction content Introduction contentIntroduction contentIntroduction
          contentIntroduction contentIntroduction contentIntroduction contentIntroduction
          contentIntroduction contentIntroduction content
        </p>
      </div>

      {/* 步骤导航 */}
      <div className="steps-container">
        <Steps
          progressDot
          className="custom-steps"
          current={currentStep}
          items={[
            {
              title: 'introduction',
            },
            {
              title: 'Data Preparation',
            },
            {
              title: 'Report Download',
            },
          ]}
        />
      </div>

      {/* 动态内容区域 */}
      <div className="content-container">
        <StepComponent />
      </div>
    </div>
  )
}
