/* Frame 2053142239 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 24px;

position: absolute;
width: 1296px;
height: 1154px;
left: 77px;
top: 437px;



/* Frame 2053142598 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 24px;

width: 1296px;
height: 707px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Visual results */

width: 153px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 24px;
line-height: 22px;
/* identical to box height, or 92% */

color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053142238 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 16px;

width: 1296px;
height: 661px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 2053142237 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: flex-end;
padding: 0px;
gap: 347px;

width: 1296px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 2053142233 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 16px;

width: 1198px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* Frame 2053141837 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 1198px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141834 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 229px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Difference Analysis Method： */

width: 229px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Select

选择器, Раскрывающийся список
Select components are used for collecting user provided information from a list of options.
*/

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 4px 12px;

width: 210px;
height: 34px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* selection-item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 1px 0px;
gap: 10px;

width: 169.82px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* title */

width: 56px;
height: 24px;

/* H5/regular */
font-family: 'Roboto';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */

/* Character/Disabled & Placeholder .25 */
color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px 0px 0px 6px;
gap: 10px;

width: 16.18px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: -0.91px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.58%;
right: 7.6%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* Frame 2053141835 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 225px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Correction Methods： */

width: 225px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Select

选择器, Раскрывающийся список
Select components are used for collecting user provided information from a list of options.
*/

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 4px 12px;

width: 210px;
height: 34px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* selection-item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 1px 0px;
gap: 10px;

width: 169.82px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* title */

width: 22px;
height: 24px;

/* H5/regular */
font-family: 'Roboto';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */

/* Character/Disabled & Placeholder .25 */
color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px 0px 0px 6px;
gap: 10px;

width: 16.18px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: -0.91px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.58%;
right: 7.6%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* Frame 2053141836 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 277px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Enrichment analysis method： */

width: 277px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Select

选择器, Раскрывающийся список
Select components are used for collecting user provided information from a list of options.
*/

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 4px 12px;

width: 210px;
height: 34px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* selection-item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 1px 0px;
gap: 10px;

width: 169.82px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* title */

width: 21px;
height: 24px;

/* H5/regular */
font-family: 'Roboto';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */

/* Character/Disabled & Placeholder .25 */
color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px 0px 0px 6px;
gap: 10px;

width: 16.18px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: -0.91px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.58%;
right: 7.6%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* Button */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px 15px;
gap: 4px;

width: 98px;
height: 32px;

background: #1975FF;
border: 1px solid #1975FF;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 66px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
text-align: center;

/* Fixed/F */
color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* 2.Desktop Charts /Charts with Grid/Bubble Charts/ Bubble Chart */

width: 1296px;
height: 580px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Bg */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;

background: #FFFFFF;


/* Graphic */

position: absolute;
left: 12px;
right: 56px;
top: 48px;
bottom: 64px;



/* Grid */

position: absolute;
left: 44px;
right: 0px;
top: 0px;
bottom: 0px;



/* Vertical Lines */

position: absolute;
left: 0px;
right: 0px;
top: 1px;
bottom: -24px;



/* *Row */

position: absolute;
left: 92.86%;
right: 0%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 85.71%;
right: 7.14%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 78.57%;
right: 14.29%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 71.43%;
right: 21.43%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 64.29%;
right: 28.57%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 57.14%;
right: 35.71%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 50%;
right: 42.86%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 42.86%;
right: 50%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 35.71%;
right: 57.14%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 28.57%;
right: 64.29%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 21.43%;
right: 71.43%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 14.29%;
right: 78.57%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 7.14%;
right: 85.71%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *Row */

position: absolute;
left: 0%;
right: 92.86%;
top: 0%;
bottom: 0%;



/* Row */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Container */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 6%;



/*  Number */

position: absolute;
height: 20px;
left: 0px;
right: 24.57px;
bottom: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
width: 1px;
left: 0px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* Line */

position: absolute;
width: 1px;
right: -0.43px;
top: 0px;
bottom: 24px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* Horizontal Lines */

position: absolute;
left: -36px;
right: 0px;
top: 0px;
bottom: 0px;



/* *row2 */

position: absolute;
height: 106px;
left: 0px;
right: 0px;
bottom: 0px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
visibility: hidden;
left: 3.69%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D8D8D8;
border: 1px solid #979797;


/* Text */

position: absolute;
width: 32px;
height: 19px;
left: 0px;
top: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
height: 1px;
left: 36px;
right: 0px;
bottom: 0px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* Line */

position: absolute;
height: 1px;
left: 36px;
right: 0px;
top: 0px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *row2 */

position: absolute;
left: 0%;
right: 0%;
top: 50%;
bottom: 25%;



/* Rectangle */

box-sizing: border-box;

position: absolute;
visibility: hidden;
left: 3.69%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D8D8D8;
border: 1px solid #979797;


/* Text */

position: absolute;
width: 32px;
height: 19px;
left: 0px;
top: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
height: 1px;
left: 36px;
right: 0px;
bottom: 0px;



/* Line */

position: absolute;
height: 1px;
left: 36px;
right: 0px;
top: 0px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *row2 */

position: absolute;
left: 0%;
right: 0%;
top: 25%;
bottom: 50%;



/* Rectangle */

box-sizing: border-box;

position: absolute;
visibility: hidden;
left: 3.69%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D8D8D8;
border: 1px solid #979797;


/* Text */

position: absolute;
width: 32px;
height: 19px;
left: 0px;
top: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
height: 1px;
left: 36px;
right: 0px;
bottom: 0px;



/* Line */

position: absolute;
height: 1px;
left: 36px;
right: 0px;
top: 0px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* *row2 */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 75%;



/* Rectangle */

box-sizing: border-box;

position: absolute;
visibility: hidden;
left: 3.69%;
right: 0%;
top: 0%;
bottom: 0%;

background: #D8D8D8;
border: 1px solid #979797;


/* Text */

position: absolute;
width: 32px;
height: 19px;
left: 0px;
top: 0px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* or 200% */

color: #9AA1A9;



/* Line */

position: absolute;
visibility: hidden;
height: 1px;
left: 36px;
right: 0px;
bottom: 0px;



/* Line */

position: absolute;
height: 1px;
left: 36px;
right: 0px;
top: 0px;



/* Rectangle */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 0px;

border: 1px solid #E9EBF1;


/* Chart */

position: absolute;
left: 44px;
right: 0px;
top: 0px;
bottom: 0px;



/* Rectangle */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;

background: rgba(255, 255, 255, 0.0001);


/* Shape */

position: absolute;
left: 57px;
right: 59px;
top: 72px;
bottom: 78px;



/* Shape */

position: absolute;
width: 24px;
height: 24px;
left: calc(50% - 24px/2 - 334px);
top: calc(50% - 24px/2 + 147px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 40px;
height: 40px;
left: calc(50% - 40px/2 - 199px);
top: calc(50% - 40px/2 + 2px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 21px;
height: 21px;
left: calc(50% - 21px/2 + 336.5px);
top: calc(50% - 21px/2 - 81.5px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 16px;
height: 16px;
left: calc(50% - 16px/2 + 403px);
top: calc(50% - 16px/2 - 151px);

background: #1975FF;


/* Shape */

position: absolute;
width: 21px;
height: 21px;
left: calc(50% - 21px/2 - 65.5px);
top: calc(50% - 21px/2 - 77.5px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 14px;
height: 14px;
left: calc(50% - 14px/2 - 267px);
top: calc(50% - 14px/2 + 51px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 14px;
height: 14px;
left: calc(50% - 14px/2 + 135px);
top: calc(50% - 14px/2 + 19px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 10px;
height: 10px;
left: calc(50% - 10px/2 - 400px);
top: calc(50% - 10px/2 + 109px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 10px;
height: 10px;
left: calc(50% - 10px/2 + 1px);
top: calc(50% - 10px/2 - 117px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 10px;
height: 10px;
left: calc(50% - 10px/2 + 269px);
top: calc(50% - 10px/2 - 45px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 9px;
height: 9px;
left: calc(50% - 9px/2 + 68.5px);
top: calc(50% - 9px/2 - 138.5px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 14px;
height: 14px;
left: calc(50% - 14px/2 - 133px);
top: calc(50% - 14px/2 - 42px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 14px;
height: 14px;
left: calc(50% - 14px/2 + 403px);
top: calc(50% - 14px/2 - 112px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 10px;
height: 10px;
left: calc(50% - 10px/2 + 202px);
top: calc(50% - 10px/2 - 124px);

background: #F7DD32;


/* Shape */

position: absolute;
width: 14px;
height: 14px;
left: calc(50% - 14px/2 + 1px);
top: calc(50% - 14px/2 - 85px);

background: #1975FF;


/* Shape */

position: absolute;
width: 14px;
height: 14px;
left: calc(50% - 14px/2 + 202px);
top: calc(50% - 14px/2 - 18px);

background: #1975FF;


/* Shape */

position: absolute;
width: 14px;
height: 14px;
left: calc(50% - 14px/2 - 199px);
top: calc(50% - 14px/2 + 109px);

background: #1975FF;


/* Shape */

position: absolute;
width: 9px;
height: 9px;
left: calc(50% - 9px/2 - 266.5px);
top: calc(50% - 9px/2 + 146.5px);

background: #1975FF;


/* Shape */

position: absolute;
width: 9px;
height: 9px;
left: calc(50% - 9px/2 + 68.5px);
top: calc(50% - 9px/2 - 100.5px);

background: #1975FF;


/* Shape */

position: absolute;
width: 9px;
height: 9px;
left: calc(50% - 9px/2 - 132.5px);
top: calc(50% - 9px/2 - 96.5px);

background: #1975FF;


/* Shape */

position: absolute;
width: 9px;
height: 9px;
left: calc(50% - 9px/2 + 335.5px);
top: calc(50% - 9px/2 - 28.5px);

background: #1975FF;


/* Shape */

position: absolute;
width: 11px;
height: 11px;
left: calc(50% - 11px/2 - 333.5px);
top: calc(50% - 11px/2 + 83.5px);

background: #1975FF;


/* Shape */

position: absolute;
width: 34px;
height: 34px;
left: calc(50% - 34px/2 + 269px);
top: calc(50% - 34px/2 - 139px);

background: #1975FF;


/* Shape */

position: absolute;
width: 21px;
height: 21px;
left: calc(50% - 21px/2 + 135.5px);
top: calc(50% - 21px/2 - 50.5px);

background: #1975FF;


/* Shape */

position: absolute;
width: 10px;
height: 10px;
left: calc(50% - 10px/2 - 66px);
top: calc(50% - 10px/2 - 33px);

background: #1975FF;


/* Shape */

position: absolute;
width: 21px;
height: 21px;
left: calc(50% - 21px/2 - 400.5px);
top: calc(50% - 21px/2 + 54.5px);

background: #1975FF;


/* Line */

position: absolute;
height: 20px;
left: 0px;
right: 0px;
top: calc(50% - 20px/2 + 142px);



/* Line */

box-sizing: border-box;

position: absolute;
height: 1px;
left: 42px;
right: 0px;
top: calc(50% - 1px/2 + 0.5px);

border: 1px dashed #DADADA;


/* Number */

position: absolute;
width: 30px;
left: 0px;
top: 0%;
bottom: 0%;



/* Color */

position: absolute;
left: 0%;
right: 0%;
top: 10%;
bottom: 10%;

background: #616C7C;
border-radius: 3px;


/* Number */

position: absolute;
width: 30px;
height: 20px;
left: 0px;
top: calc(50% - 20px/2);

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 10px;
line-height: 20px;
/* identical to box height, or 200% */
text-align: center;

color: #FFFFFF;



/* Hover Elements */

position: absolute;
width: 58px;
height: 36px;
left: 284px;
top: 366px;



/* Bubble */

position: absolute;
width: 58px;
height: 36px;
left: calc(50% - 58px/2 - 335px);
top: calc(50% - 36px/2 + 94px);



/* Pointer */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/*  Left */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Bubble */

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: -6px;

filter: drop-shadow(0px 10px 24px rgba(29, 42, 68, 0.12));


/* BG */

position: absolute;
left: 0%;
right: 0%;
top: 0px;
bottom: 0.04px;

background: #FFFFFF;


/* Rectangle  */

box-sizing: border-box;

position: absolute;
left: 0px;
right: 0px;
top: 0px;
bottom: 6px;

background: #FFFFFF;
border: 1px solid #E9EBF1;
border-radius: 4px;


/* Triangle */

box-sizing: border-box;

position: absolute;
width: 14px;
height: 8px;
left: calc(50% - 14px/2 - 1px);
bottom: -1px;

background: #FFFFFF;
border: 1px solid #E9EBF1;
transform: rotate(-180deg);


/* Text */

position: absolute;
height: 20px;
left: 8px;
right: 8px;
top: calc(50% - 20px/2 - 1px);

font-family: 'OPPOSans';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 20px;
/* identical to box height, or 167% */
text-align: center;

color: #505D6F;



/* Frame 2053142599 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 24px;

width: 1296px;
height: 423px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Data analysis results */

width: 237px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 24px;
line-height: 22px;
/* identical to box height, or 92% */

color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053142597 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;
gap: 16px;

width: 1296px;
height: 377px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Button */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: flex-end;
align-items: center;
padding: 0px 15px;
gap: 4px;

width: 98px;
height: 32px;

background: #1975FF;
border: 1px solid #1975FF;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 66px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
text-align: center;

/* Fixed/F */
color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* table */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

width: 1296px;
height: 329px;

border: 1px solid #000000;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 1px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
display: none;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

/* Conditional/divider */
border: 1px solid rgba(0, 0, 0, 0.06);
transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 83px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 83px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 83px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 83px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 83px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 83px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 83px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 83px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 83px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 83px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141761 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 83px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 83px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 8;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Components/Table-Column/Text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 129.6px;
height: 329px;

background: #FFFFFF;
border-width: 1px 1px 0px 0px;
border-style: solid;
border-color: rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 9;
flex-grow: 1;


/* Components/Table-Cell/Header */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 12px;
gap: 4px;
isolation: isolate;

width: 129.6px;
height: 47px;

background: rgba(0, 0, 0, 0.02);
/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* title */

width: 105.6px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;
z-index: 0;


/* Divider ↕︎ */

position: absolute;
width: 22px;
height: 0px;
left: 0px;
top: calc(50% - 0px/2 - 10.5px);

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* text */

width: 34px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* text */

width: 23px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* text */

width: 31px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* table-cell/text */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 12px;

width: 129.6px;
height: 47px;

/* Conditional/divider */
border-bottom: 1px solid rgba(0, 0, 0, 0.06);

/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* text */

width: 29px;
height: 22px;

font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
