/* 根据设计稿精确还原的样式 */
.report-download-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 48px;
  width: 100%;
  max-width: 1296px;
  margin: 0 auto;
  font-family:
    'SF Pro',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;

  .section-title {
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
  }

  /* Visual results section */
  .visual-results-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 24px;
    width: 100%;
    align-self: stretch;

    /* Controls section */
    .controls-section {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 0px;
      gap: 24px;
      width: 100%;
      align-self: stretch;

      .controls-row1 {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        padding: 0px;
        gap: 8px;
        width: 100%;
        align-self: stretch;

        .control-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          padding: 0px;
          gap: 12px;
          flex: none;

          &:nth-child(1) {
            width: 229px;
          }

          &:nth-child(2) {
            width: 225px;
          }

          &:nth-child(3) {
            width: 277px;
          }

          &.generate-item {
            width: auto;
            flex: none;

            .generate-spacer {
              height: 19px;
            }
          }

          .control-label {
            font-family: 'SF Pro';
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 19px;
            color: rgba(0, 0, 0, 0.85);
            margin: 0;
            white-space: nowrap;
          }
        }
      }
    }

    /* Chart section */
    .chart-section {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 0px;
      gap: 0px;
      width: 100%;
      align-self: stretch;

      .chart-container {
        position: relative;
        width: 100%;
        max-width: 1296px;

        .chart-image {
          width: 100%;
          height: 580px;
          object-fit: cover;
          border-radius: 8px;
          border: 1px solid rgba(0, 0, 0, 0.06);
          background: #fafafa;
          display: block;
        }

        .chart-download-overlay {
          position: absolute;
          top: 16px;
          right: 16px;
          opacity: 0;
          transition: opacity 0.3s ease;

          .chart-download-button {
            background: rgba(25, 117, 255, 0.9);
            border: none;
            backdrop-filter: blur(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

            &:hover {
              background: rgba(25, 117, 255, 1) !important;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
            }
          }
        }

        &:hover .chart-download-overlay {
          opacity: 1;
        }
      }
    }
  }

  .control-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 12px;
    flex: none;
  }

  .control-item:nth-child(1) {
    width: 229px;
  }

  .control-item:nth-child(2) {
    width: 225px;
  }

  .control-item:nth-child(3) {
    width: 277px;
  }

  .control-item.generate-item {
    width: auto;
    flex: none;
  }

  .generate-spacer {
    height: 19px;
  }

  .control-label {
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
    white-space: nowrap;
  }

  /* Ant Design Select 样式覆盖 */
  .control-select {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }

  .control-select .ant-select-selector {
    border: 1px solid #d9d9d9 !important;
    border-radius: 2px !important;
    padding: 4px 12px !important;
    height: 34px !important;
    display: flex !important;
    align-items: center !important;
  }

  .control-select .ant-select-selection-item {
    color: rgba(0, 0, 0, 0.85) !important;
  }

  .control-select:hover .ant-select-selector {
    border-color: #1975ff !important;
  }

  .control-select.ant-select-focused .ant-select-selector {
    border-color: #1975ff !important;
    box-shadow: 0 0 0 2px rgba(25, 117, 255, 0.2) !important;
  }

  /* Generate button */
  .generate-button {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px 15px;
    gap: 4px;
    width: 88px;
    height: 34px;
    background: #1975ff;
    border: 1px solid #1975ff;
    border-radius: 4px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #ffffff;
  }

  .generate-button:hover {
    background: #4096ff !important;
    border-color: #4096ff !important;
  }

  /* Chart section */
  .chart-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 0px;
    width: 100%;
    align-self: stretch;
  }

  .chart-container {
    position: relative;
    width: 100%;
    max-width: 1296px;
  }

  .chart-image {
    width: 100%;
    height: 580px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    background: #fafafa;
    display: block;
  }

  .chart-download-overlay {
    position: absolute;
    top: 16px;
    right: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .chart-container:hover .chart-download-overlay {
    opacity: 1;
  }

  .chart-download-button {
    background: rgba(25, 117, 255, 0.9);
    border: none;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .chart-download-button:hover {
    background: rgba(25, 117, 255, 1) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  }

  /* Data analysis results section */
  .data-results-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 24px;
    width: 100%;
    align-self: stretch;
  }

  .results-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0px;
    gap: 24px;
    width: 100%;
    align-self: stretch;
  }

  /* Download button */
  .download-button {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px 15px;
    gap: 4px;
    width: 108px;
    height: 34px;
    background: #1975ff;
    border: 1px solid #1975ff;
    border-radius: 4px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #ffffff;
  }

  .download-button:hover {
    background: #4096ff !important;
    border-color: #4096ff !important;
  }

  /* Results table container */
  .results-table-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 0px;
    width: 100%;
    align-self: stretch;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    overflow: hidden;
  }

  /* Results table styles */
  .results-table {
    width: 100%;
    font-family:
      'SF Pro',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
  }

  .results-table .ant-table {
    font-size: 14px;
    border: none;
  }

  .results-table .ant-table-container {
    border: none;
  }

  .results-table .ant-table-thead > tr > th {
    background: #fafafa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
    padding: 12px 16px;
  }

  .results-table .ant-table-thead > tr > th:last-child {
    border-right: none;
  }

  .results-table .ant-table-tbody > tr > td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
    padding: 12px 16px;
  }

  .results-table .ant-table-tbody > tr > td:last-child {
    border-right: none;
  }

  .results-table .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }

  /* 表格滚动条样式 */
  .results-table .ant-table-body::-webkit-scrollbar,
  .results-table .ant-table-content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .results-table .ant-table-body::-webkit-scrollbar-track,
  .results-table .ant-table-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .results-table .ant-table-body::-webkit-scrollbar-thumb,
  .results-table .ant-table-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .results-table .ant-table-body::-webkit-scrollbar-thumb:hover,
  .results-table .ant-table-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .controls-row1 {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .control-item {
      width: 100% !important;
    }

    .control-item:nth-child(1),
    .control-item:nth-child(2),
    .control-item:nth-child(3) {
      width: 100% !important;
    }

    .control-select {
      width: 100% !important;
    }

    .generate-button {
      align-self: flex-start;
    }

    .results-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .chart-image {
      height: 300px;
    }

    .chart-download-overlay {
      opacity: 1;
      position: static;
      margin-top: 16px;
    }
  }

  /* 下拉菜单样式 */
  .ant-dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .ant-dropdown-menu-item {
    padding: 8px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
  }

  .ant-dropdown-menu-item:hover {
    background: rgba(25, 117, 255, 0.1);
    color: #1975ff;
  }

  .ant-dropdown-menu-item span {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }

  /* 底部导航按钮 */
  .navigation-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0px;
    gap: 16px;
    width: 100%;
    margin-top: 48px;
  }

  .nav-button {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px 15px;
    gap: 4px;
    min-width: 88px;
    height: 32px;
    border-radius: 4px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
  }

  .previous-button {
    background: #ffffff;
    border: 1px solid #d9d9d9;
    box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.016);
    color: #030712;
  }

  .next-button {
    background: #1975ff;
    border: 1px solid #1975ff;
    color: #ffffff;
  }

  .previous-button:hover {
    border-color: #1975ff !important;
    color: #1975ff !important;
  }

  .next-button:hover {
    background: #4096ff !important;
    border-color: #4096ff !important;
  }
}
