import { useState } from 'react'
import { Button, Select, Table, Dropdown } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import './index.css'

const { Option } = Select

interface ReportDownloadProps {
  onPrevious?: () => void
  onNext?: () => void
}

export default function ReportDownload({ onPrevious, onNext }: ReportDownloadProps) {
  const [differenceMethod, setDifferenceMethod] = useState<string>('DESeq2')
  const [correctionMethod, setCorrectionMethod] = useState<string>('FDR')
  const [enrichmentMethod, setEnrichmentMethod] = useState<string>('GSEA')

  // 模拟表格数据
  const tableColumns = [
    {
      title: 'Gene Symbol',
      dataIndex: 'geneSymbol',
      key: 'geneSymbol',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: 'Log2FC',
      dataIndex: 'log2fc',
      key: 'log2fc',
      width: 100,
    },
    {
      title: 'P-value',
      dataIndex: 'pvalue',
      key: 'pvalue',
      width: 100,
    },
    {
      title: 'Adj P-value',
      dataIndex: 'adjPvalue',
      key: 'adjPvalue',
      width: 120,
    },
    {
      title: 'Expression',
      dataIndex: 'expression',
      key: 'expression',
      width: 100,
    },
    {
      title: 'Pathway',
      dataIndex: 'pathway',
      key: 'pathway',
      width: 150,
    },
    {
      title: 'GO Term',
      dataIndex: 'goTerm',
      key: 'goTerm',
      width: 200,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: 250,
    },
  ]

  const tableData = Array.from({ length: 50 }, (_, index) => ({
    key: index,
    geneSymbol: `GENE${index + 1}`,
    log2fc: (Math.random() * 4 - 2).toFixed(2),
    pvalue: (Math.random() * 0.05).toExponential(2),
    adjPvalue: (Math.random() * 0.01).toExponential(2),
    expression: (Math.random() * 1000).toFixed(0),
    pathway: `Pathway ${(index % 5) + 1}`,
    goTerm: `GO:${String(index + 1000).padStart(7, '0')}`,
    description: `Gene ${index + 1} functional description and biological process annotation`,
  }))

  const handleGenerate = () => {
    console.log('Generate report with:', {
      differenceMethod,
      correctionMethod,
      enrichmentMethod,
    })
  }

  // 图表下载选项
  const chartDownloadOptions = [
    { key: 'pdf', label: 'Download as PDF' },
    { key: 'png', label: 'Download as PNG' },
    { key: 'svg', label: 'Download as SVG' },
  ]

  // 表格下载选项
  const tableDownloadOptions = [{ key: 'csv', label: 'Download as CSV' }]

  const handleChartDownload = (format: string) => {
    console.log(`Downloading chart as ${format}`)
    // 这里实现图表下载逻辑
  }

  const handleTableDownload = (format: string) => {
    console.log(`Downloading table as ${format}`)
    // 这里实现表格下载逻辑
  }

  const handleDownload = () => {
    console.log('Download report')
    // 这里可以实现实际的下载逻辑
  }

  return (
    <div className="report-download-container">
      {/* Visual results section */}
      <div className="visual-results-section">
        <h2 className="section-title">Visual results</h2>

        <div className="controls-section">
          <div className="controls-row1 flex items-center justify-between">
            <div className="flex items-center">
              <div className="control-item">
                <label className="control-label">Difference Analysis Method：</label>
                <Select
                  className="control-select"
                  value={differenceMethod}
                  onChange={setDifferenceMethod}
                  style={{ width: 210, height: 34 }}
                >
                  <Option value="DESeq2">DESeq2</Option>
                  <Option value="EdgeR">EdgeR</Option>
                  <Option value="Limma">Limma</Option>
                </Select>
              </div>

              <div className="control-item">
                <label className="control-label">Correction Methods：</label>
                <Select
                  className="control-select"
                  value={correctionMethod}
                  onChange={setCorrectionMethod}
                  style={{ width: 210, height: 34 }}
                >
                  <Option value="FDR">FDR</Option>
                  <Option value="Bonferroni">Bonferroni</Option>
                  <Option value="Holm">Holm</Option>
                  <Option value="Hochberg">Hochberg</Option>
                </Select>
              </div>

              <div className="control-item">
                <label className="control-label">Enrichment analysis method：</label>
                <Select
                  className="control-select"
                  value={enrichmentMethod}
                  onChange={setEnrichmentMethod}
                  style={{ width: 210, height: 34 }}
                >
                  <Option value="GSEA">GSEA</Option>
                  <Option value="ORA">ORA</Option>
                  <Option value="fGSEA">fGSEA</Option>
                  <Option value="CAMERA">CAMERA</Option>
                </Select>
              </div>

              <div className="control-item generate-item">
                <div className="generate-spacer"></div>
                <Button type="primary" className="generate-button" onClick={handleGenerate}>
                  Generate
                </Button>
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                className="download-button"
                onClick={handleDownload}
              >
                Copy
              </Button>
              <Dropdown
                menu={{
                  items: chartDownloadOptions.map(option => ({
                    key: option.key,
                    label: (
                      <span onClick={() => handleChartDownload(option.key)}>
                        {option.icon} {option.label}
                      </span>
                    ),
                  })),
                }}
                trigger={['hover']}
                placement="bottomRight"
              >
                <Button type="primary" icon={<DownloadOutlined />} className="download-button">
                  Download
                </Button>
              </Dropdown>
            </div>
          </div>
        </div>

        {/* Chart area */}
        <div className="chart-section">
          <div className="chart-container">
            <img
              src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1296&h=580&q=80"
              alt="Data Visualization Chart"
              className="chart-image"
              onError={e => {
                // 如果图片加载失败，使用占位图
                e.currentTarget.src =
                  'https://via.placeholder.com/1296x580/f5f5f5/1975ff?text=Data+Visualization+Chart'
              }}
            />
          </div>
        </div>
      </div>

      {/* Data analysis results section */}
      <div className="data-results-section">
        <div className="results-header">
          <h2 className="section-title">Data analysis results</h2>
          <div className="control-item generate-item">
            <Dropdown
              menu={{
                items: tableDownloadOptions.map(option => ({
                  key: option.key,
                  label: (
                    <span onClick={() => handleTableDownload(option.key)}>
                      {option.icon} {option.label}
                    </span>
                  ),
                })),
              }}
              trigger={['hover']}
              placement="bottomRight"
            >
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                className="download-button"
                onClick={handleDownload}
              >
                Download
              </Button>
            </Dropdown>
          </div>
        </div>

        {/* Results table */}
        <div className="results-table-container">
          <Table
            columns={tableColumns}
            dataSource={tableData}
            pagination={false}
            scroll={{ x: 'max-content', y: 329 }}
            size="small"
            bordered
            className="results-table"
          />
        </div>
      </div>

      {/* 底部导航按钮 */}
      <div className="navigation-buttons">
        <Button className="nav-button previous-button" onClick={onPrevious}>
          Previous
        </Button>
        <Button type="primary" className="nav-button next-button" onClick={onNext}>
          Next
        </Button>
      </div>
    </div>
  )
}
