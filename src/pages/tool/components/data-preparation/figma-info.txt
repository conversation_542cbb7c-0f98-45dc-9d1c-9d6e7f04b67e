/* Frame 2053142235 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;
gap: 48px;

width: 763px;
height: 591px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 2053142234 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 24px;

width: 763px;
height: 511px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 2053142233 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 16px;

width: 763px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141837 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 763px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141834 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 229px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Difference Analysis Method： */

width: 229px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Select

选择器, Раскрывающийся список
Select components are used for collecting user provided information from a list of options.
*/

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 4px 12px;

width: 210px;
height: 34px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* selection-item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 1px 0px;
gap: 10px;

width: 169.82px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* title */

width: 56px;
height: 24px;

/* H5/regular */
font-family: 'Roboto';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */

/* Character/Disabled & Placeholder .25 */
color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px 0px 0px 6px;
gap: 10px;

width: 16.18px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: -0.91px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.58%;
right: 7.6%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* Frame 2053141835 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 225px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Correction Methods： */

width: 225px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Select

选择器, Раскрывающийся список
Select components are used for collecting user provided information from a list of options.
*/

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 4px 12px;

width: 210px;
height: 34px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* selection-item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 1px 0px;
gap: 10px;

width: 169.82px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* title */

width: 22px;
height: 24px;

/* H5/regular */
font-family: 'Roboto';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */

/* Character/Disabled & Placeholder .25 */
color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px 0px 0px 6px;
gap: 10px;

width: 16.18px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: -0.91px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.58%;
right: 7.6%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* Frame 2053141836 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 277px;
height: 65px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Enrichment analysis method： */

width: 277px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Select

选择器, Раскрывающийся список
Select components are used for collecting user provided information from a list of options.
*/

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 4px 12px;

width: 210px;
height: 34px;

/* Neutral/1 */
background: #FFFFFF;
/* Neutral/5 */
border: 1px solid #D9D9D9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* selection-item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 1px 0px;
gap: 10px;

width: 169.82px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* title */

width: 21px;
height: 24px;

/* H5/regular */
font-family: 'Roboto';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */

/* Character/Disabled & Placeholder .25 */
color: rgba(0, 0, 0, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px 0px 0px 6px;
gap: 10px;

width: 16.18px;
height: 26px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* icon */

width: 10.18px;
height: 6.86px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Down */

position: absolute;
width: 12px;
height: 12px;
right: -0.91px;
top: calc(50% - 12px/2);



/* Vector */

position: absolute;
left: 7.58%;
right: 7.6%;
top: 21.43%;
bottom: 21.43%;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);


/* Upload-Drag */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 763px;
height: 422px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* button */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 16px 0px;
gap: 20px;

width: 763px;
height: 332px;

/* Neutral/2 */
background: #FAFAFA;
border: 1px dashed #D9D9D9;
box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
border-radius: 8px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Inbox */

width: 57px;
height: 57px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 13.43%;
right: 13.41%;
top: 12.99%;
bottom: 13.03%;

background: #1975FF;


/* text-wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 4px;

width: 395px;
height: 72px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Click or drag file to this area to upload */

width: 395px;
height: 24px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 18px;
line-height: 24px;
/* identical to box height, or 133% */
text-align: center;

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Support for a single or bulk upload. Strictly prohibit from uploading company data or other band files */

width: 395px;
height: 44px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* or 157% */
text-align: center;

/* Character / Secondary .45 */
color: rgba(0, 0, 0, 0.45);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Upload-List/File */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 763px;
height: 82px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Upload-File-List-Item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
gap: 8px;

width: 763px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* icon-attachment */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.28%;
right: 12.28%;
top: 6.93%;
bottom: 6.9%;

/* Character / Secondary .45 */
background: rgba(0, 0, 0, 0.45);


/* filename */

width: 733px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Primary/6 */
color: #1890FF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Upload-File-List-Item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
gap: 8px;

width: 763px;
height: 22px;

/* Neutral/2 */
background: #FAFAFA;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* wrapper */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 723px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* icon-attachment */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.28%;
right: 12.28%;
top: 6.93%;
bottom: 6.9%;

/* Character / Secondary .45 */
background: rgba(0, 0, 0, 0.45);


/* filename */

width: 701px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Primary/6 */
color: #1890FF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* icon */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 3px 5px;
gap: 10px;

width: 24px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-delete */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 7.14%;
right: 7.14%;
top: 5.35%;
bottom: 5.37%;

/* Character / Secondary .45 */
background: rgba(0, 0, 0, 0.45);


/* Upload-File-List-Item */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 4px;
gap: 8px;

width: 763px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* wrapper */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 723px;
height: 22px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* icon-attachment */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.28%;
right: 12.28%;
top: 6.93%;
bottom: 6.9%;

/* Dust Red / 5

Dust Red（薄暮）FF4D4F
*/
background: #FF4D4F;


/* filename */

width: 701px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* Dust Red / 5 */
color: #FF4D4F;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* icon */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 3px 5px;
gap: 10px;

width: 24px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-delete */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 7.14%;
right: 7.14%;
top: 5.35%;
bottom: 5.37%;

/* Dust Red / 5

Dust Red（薄暮）FF4D4F
*/
background: #FF4D4F;


/* Frame 2053142231 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 24px;

width: 174px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Button */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px 15px;
gap: 4px;

width: 88px;
height: 32px;

/* Basic_light蓝黑/0

FFFFFF
*/
background: #FFFFFF;
border: 1px solid #D9D9D9;
/* drop-shadow/button-secondary */
box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.016);
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 56px;
height: 22px;

/* 段落（R）/P3 */
font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
text-align: center;

color: #030712;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Button */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px 15px;
gap: 4px;

width: 62px;
height: 32px;

background: #1975FF;
border: 1px solid #1975FF;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

width: 30px;
height: 22px;

/* 段落（R）/P3 */
font-family: 'PingFang SC';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
text-align: center;

/* Fixed/F */
color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Icon-Wrapper */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

display: none;
width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
