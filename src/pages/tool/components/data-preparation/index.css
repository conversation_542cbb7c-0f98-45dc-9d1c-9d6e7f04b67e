/* 根据设计稿精确还原的样式 */
.data-preparation-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 0px;
  gap: 48px;
  width: 100%;
  max-width: 763px;
  margin: 0 auto;
  font-family:
    'SF Pro',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;

  /* 配置选择器区域 */
  .config-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 24px;
    width: 100%;
    align-self: stretch;

    .config-row {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      padding: 0px;
      gap: 8px;
      width: 100%;
      align-self: stretch;

      .config-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        gap: 12px;
        flex: none;
        min-width: 0;

        &:nth-child(1) {
          width: 229px;
        }

        &:nth-child(2) {
          width: 225px;
        }

        &:nth-child(3) {
          width: 277px;
        }
      }
    }
  }

  .config-label {
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
    white-space: nowrap;
  }

  /* Ant Design Select 样式覆盖 */
  .config-select-antd {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;

    .ant-select-selector {
      border: 1px solid #d9d9d9 !important;
      border-radius: 2px !important;
      padding: 4px 12px !important;
      height: 34px !important;
      display: flex !important;
      align-items: center !important;
    }

    .ant-select-selection-placeholder {
      color: rgba(0, 0, 0, 0.25) !important;
    }

    .ant-select-selection-item {
      color: rgba(0, 0, 0, 0.85) !important;
    }

    &:hover .ant-select-selector {
      border-color: #1975ff !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #1975ff !important;
      box-shadow: 0 0 0 2px rgba(25, 117, 255, 0.2) !important;
    }
  }

  /* 文件上传区域 */
  .upload-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 8px;
    width: 100%;
    align-self: stretch;

    .upload-dragger {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 16px 0px;
      gap: 20px;
      width: 100%;
      height: 332px;
      background: #fafafa;
      border: 1px dashed #d9d9d9;
      box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover,
      &.drag-over {
        border-color: #1975ff;
        background: #f0f8ff;
      }

      &.drag-over {
        border-style: solid;
        transform: scale(1.02);
      }

      .upload-icon {
        width: 57px;
        height: 57px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .upload-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0px;
        gap: 8px;

        .upload-title {
          font-family: 'Roboto';
          font-style: normal;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          text-align: center;
          color: rgba(0, 0, 0, 0.85);
          margin: 0;
        }

        .upload-subtitle {
          font-family: 'Roboto';
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          text-align: center;
          color: rgba(0, 0, 0, 0.45);
          margin: 0;
        }
      }
    }
  }

  .upload-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0px;
    gap: 4px;
    width: 395px;
    height: 72px;
  }

  .upload-title {
    width: 395px;
    height: 24px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    text-align: center;
    color: rgba(0, 0, 0, 0.85);
  }

  .upload-subtitle {
    width: 395px;
    height: 44px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
  }

  /* 文件列表 */
  .file-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 8px;
    width: 100%;
    align-self: stretch;
  }

  .file-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 12px;
    gap: 8px;
    width: 100%;
    height: 22px;
    align-self: stretch;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    cursor: pointer;
    box-sizing: border-box;
  }

  .file-item:hover {
    background: #f5f5f5;
  }

  .file-item.uploading {
    background: #fafafa;
  }

  .file-item.uploading:hover {
    background: #f0f0f0;
  }

  .file-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 8px;
    flex: 1;
    height: 22px;
  }

  .file-icon {
    width: 14px;
    height: 14px;
    flex: none;
  }

  .file-name {
    flex: 1;
    height: 22px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #1890ff;
  }

  .file-action {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 3px 5px;
    gap: 10px;
    width: 24px;
    height: 20px;
    flex: none;
  }

  .delete-icon {
    width: 14px;
    height: 14px;
    cursor: pointer;
    transition: color 0.3s ease;
  }

  .delete-icon:hover {
    color: #ff7875 !important;
  }

  /* 底部按钮 */
  .action-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 24px;
    width: 174px;
    height: 32px;
    flex: none;
  }

  .cancel-button {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px 15px;
    gap: 4px;
    min-width: 88px;
    height: 32px;
    background: #ffffff;
    border: 1px solid #d9d9d9;
    box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.016);
    border-radius: 4px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #030712;
  }

  .submit-button {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px 15px;
    gap: 4px;
    min-width: 62px;
    height: 32px;
    background: #1975ff;
    border: 1px solid #1975ff;
    border-radius: 4px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #ffffff;
  }

  .cancel-button:hover,
  .submit-button:hover {
    transform: translateY(-1px);
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.12);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .config-row {
    flex-direction: column;
    gap: 16px;
  }

  .config-item {
    width: 100%;
  }

  .select-wrapper {
    width: 100%;
  }

  .upload-text {
    width: 100%;
    max-width: 395px;
  }

  .upload-title,
  .upload-subtitle {
    width: 100%;
  }
}
