import React, { useState } from 'react'
import { But<PERSON>, Select } from 'antd'
import { InboxOutlined, PaperClipOutlined, DeleteOutlined } from '@ant-design/icons'
import './index.css'

const { Option } = Select

// const { Dragger } = Upload

interface FileItem {
  name: string
  status: 'success' | 'error' | 'uploading'
}

interface DataPreparationProps {
  onPrevious?: () => void
  onNext?: () => void
}

export default function DataPreparation({ onPrevious, onNext }: DataPreparationProps) {
  const [fileList, setFileList] = useState<FileItem[]>([
    { name: 'sample_data.xlsx', status: 'success' },
    { name: 'metadata.csv', status: 'uploading' },
    { name: 'invalid_file.txt', status: 'error' },
  ])

  const [differenceMethod, setDifferenceMethod] = useState<string>('')
  const [correctionMethod, setCorrectionMethod] = useState<string>('FDR')
  const [enrichmentMethod, setEnrichmentMethod] = useState<string>('GSEA')

  // const handleUpload = (info: any) => {
  //   // Handle file upload logic here
  //   console.log('Upload info:', info)
  // }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.currentTarget.classList.add('drag-over')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.currentTarget.classList.remove('drag-over')
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.currentTarget.classList.remove('drag-over')
    // Handle file drop logic here
    const files = Array.from(e.dataTransfer.files)
    console.log('Dropped files:', files)
  }

  const removeFile = (index: number) => {
    setFileList(fileList.filter((_, i) => i !== index))
  }

  return (
    <div className="data-preparation-container">
      {/* 配置选择器区域 */}
      <div className="config-section">
        <div className="config-row">
          <div className="config-item">
            <label className="config-label">Difference Analysis Method：</label>
            <Select
              className="config-select-antd"
              placeholder="Select"
              value={differenceMethod}
              onChange={setDifferenceMethod}
              style={{ width: 210, height: 34 }}
            >
              <Option value="deseq2">DESeq2</Option>
              <Option value="edger">EdgeR</Option>
              <Option value="limma">Limma</Option>
            </Select>
          </div>

          <div className="config-item">
            <label className="config-label">Correction Methods：</label>
            <Select
              className="config-select-antd"
              value={correctionMethod}
              onChange={setCorrectionMethod}
              style={{ width: 210, height: 34 }}
            >
              <Option value="FDR">FDR</Option>
              <Option value="bonferroni">Bonferroni</Option>
              <Option value="holm">Holm</Option>
              <Option value="hochberg">Hochberg</Option>
            </Select>
          </div>

          <div className="config-item">
            <label className="config-label">Enrichment analysis method：</label>
            <Select
              className="config-select-antd"
              value={enrichmentMethod}
              onChange={setEnrichmentMethod}
              style={{ width: 210, height: 34 }}
            >
              <Option value="GSEA">GSEA</Option>
              <Option value="ora">ORA</Option>
              <Option value="fgsea">fGSEA</Option>
              <Option value="camera">CAMERA</Option>
            </Select>
          </div>
        </div>
      </div>

      {/* 文件上传区域 */}
      <div className="upload-section">
        <div
          className="upload-dragger"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => {
            // Trigger file input click
            const input = document.createElement('input')
            input.type = 'file'
            input.multiple = true
            input.accept = '.xlsx,.xls,.csv,.txt'
            input.onchange = e => {
              const files = Array.from((e.target as HTMLInputElement).files || [])
              console.log('Selected files:', files)
            }
            input.click()
          }}
        >
          <div className="upload-icon">
            <InboxOutlined style={{ fontSize: '57px', color: '#1975FF' }} />
          </div>
          <div className="upload-text">
            <div className="upload-title">Click or drag file to this area to upload</div>
            <div className="upload-subtitle">
              Support for a single or bulk upload. Strictly prohibit from uploading company data or
              other band files
            </div>
          </div>
        </div>

        {/* 文件列表 */}
        <div className="file-list">
          {fileList.map((file, index) => (
            <div
              key={index}
              className={`file-item ${file.status === 'uploading' ? 'uploading' : ''}`}
            >
              <div className="file-content">
                <PaperClipOutlined
                  className="file-icon"
                  style={{
                    color: file.status === 'error' ? '#FF4D4F' : 'rgba(0,0,0,0.45)',
                  }}
                />
                <span
                  className="file-name"
                  style={{
                    color: file.status === 'error' ? '#FF4D4F' : '#1890FF',
                  }}
                >
                  {file.name}
                </span>
              </div>

              <div className="file-action">
                <DeleteOutlined
                  className="delete-icon"
                  onClick={() => removeFile(index)}
                  style={{
                    color: file.status === 'error' ? '#FF4D4F' : 'rgba(0,0,0,0.45)',
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 底部按钮 */}
      <div className="action-buttons mt-12">
        <Button className="cancel-button" onClick={onPrevious}>
          Previous
        </Button>
        <Button type="primary" className="submit-button" onClick={onNext}>
          Next
        </Button>
      </div>
    </div>
  )
}
