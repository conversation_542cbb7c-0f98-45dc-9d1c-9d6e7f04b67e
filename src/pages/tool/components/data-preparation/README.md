# Data Preparation Component

## 概述

这是一个根据 Figma 设计稿 1:1 还原的数据准备组件，用于差异表达分析的数据上传和配置。

## 功能特性

### 1. 分析方法配置

- **Difference Analysis Method**: 差异分析方法选择
  - DESeq2
  - EdgeR
  - Limma
- **Correction Methods**: 多重检验校正方法
  - FDR (默认)
  - Bonferroni
  - Holm
  - Hochberg

- **Enrichment Analysis Method**: 富集分析方法
  - GSEA (默认)
  - ORA
  - fGSEA
  - CAMERA

### 2. 文件上传功能

- 支持拖拽上传
- 支持点击选择文件
- 支持多文件上传
- 文件格式限制：.xlsx, .xls, .csv, .txt
- 实时文件状态显示：
  - 成功上传 (蓝色文件名)
  - 上传中 (灰色背景)
  - 上传失败 (红色文件名和图标)

### 3. 文件管理

- 文件列表显示
- 删除功能 (上传中和失败的文件)
- 文件状态图标

### 4. 操作按钮

- 取消按钮
- 提交按钮

## 设计规范

### 尺寸规格

- 容器最大宽度: 763px
- 配置项间距: 8px
- 垂直间距: 48px
- 上传区域高度: 332px

### 颜色规范

- 主色调: #1975FF
- 文字颜色: rgba(0,0,0,0.85)
- 次要文字: rgba(0,0,0,0.45)
- 边框颜色: #D9D9D9
- 背景色: #FAFAFA
- 错误色: #FF4D4F

### 字体规范

- 主字体: SF Pro
- 备用字体: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- 标签字体大小: 16px
- 按钮字体: PingFang SC, 14px

## 响应式设计

- 移动端适配 (768px 以下)
- 配置项垂直排列
- 选择器宽度自适应

## 交互效果

- 悬停效果
- 拖拽悬停高亮
- 按钮点击反馈
- 文件删除确认

## 使用方法

```tsx
import DataPreparation from './components/data-preparation'

function App() {
  return (
    <div>
      <DataPreparation />
    </div>
  )
}
```

## 技术栈

- React 18
- TypeScript
- Ant Design
- CSS3

## 文件结构

```
data-preparation/
├── index.tsx          # 主组件
├── index.css          # 样式文件
├── figma-info.txt     # 设计稿信息
├── figma-info.svg     # 设计稿SVG
└── README.md          # 说明文档
```
