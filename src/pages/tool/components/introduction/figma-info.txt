/* analysis tool */

position: relative;
width: 1440px;
height: 1425px;

background: #FFFFFF;


/* Line 1 */

position: absolute;
width: 1311px;
height: 0px;
left: 0px;
top: 0px;

border: 1px solid #E0D7D7;
transform: rotate(0deg);


/* 导航栏 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 20px 60px;
gap: 128px;

position: absolute;
width: 1439px;
height: 90px;
left: 0px;
top: 0px;

border-bottom: 1px solid rgba(0, 0, 0, 0.08);


/* Group 2 */

width: 243px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* pure-white-tech-altitude-icon 1 */

position: absolute;
width: 64px;
height: 61px;
left: 60px;
top: 14.5px;



/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 15%;
right: 15%;
top: 10%;
bottom: 10%;



/* Vector */

position: absolute;
left: 20%;
right: 20%;
top: 35%;
bottom: 30%;

border: 3px solid #0EA5E9;


/* Vector */

position: absolute;
left: 27.5%;
right: 72.5%;
top: 35%;
bottom: 30%;

background: #000000;
border: 1px dashed #94A3B8;


/* Vector */

position: absolute;
left: 50%;
right: 50%;
top: 25%;
bottom: 25%;

background: #000000;
border: 1px dashed #94A3B8;


/* Vector */

position: absolute;
left: 72.5%;
right: 27.5%;
top: 35%;
bottom: 30%;

background: #000000;
border: 1px dashed #94A3B8;


/* Vector */

position: absolute;
left: 25.5%;
right: 70.5%;
top: 48%;
bottom: 48%;

background: #3B82F6;


/* Vector */

position: absolute;
left: 48%;
right: 48%;
top: 33%;
bottom: 63%;

background: #3B82F6;


/* Vector */

position: absolute;
left: 70.5%;
right: 25.5%;
top: 43%;
bottom: 53%;

background: #3B82F6;


/* Vector */

position: absolute;
left: 25%;
right: 25%;
top: 57.5%;
bottom: 35%;

border: 2px solid #0284C7;


/* Group */

position: absolute;
left: 42.5%;
right: 42.5%;
top: 42.5%;
bottom: 42.5%;



/* Vector */

position: absolute;
left: 42.5%;
right: 42.5%;
top: 42.5%;
bottom: 42.5%;

border: 2px solid #0369A1;


/* Vector */

position: absolute;
left: 46.5%;
right: 46.5%;
top: 46.5%;
bottom: 46.5%;

background: #0EA5E9;
opacity: 0.7;


/* Vector */

position: absolute;
left: 48.5%;
right: 48.5%;
top: 48.5%;
bottom: 48.5%;

background: #0284C7;


/* Group */

position: absolute;
left: 27%;
right: 27%;
top: 22%;
bottom: 23%;

opacity: 0.3;


/* Vector */

position: absolute;
left: 29.5%;
right: 69.5%;
top: 24.5%;
bottom: 74.5%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 34.5%;
right: 64.5%;
top: 22%;
bottom: 77%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 39.5%;
right: 59.5%;
top: 27%;
bottom: 72%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 44.5%;
right: 54.5%;
top: 23.5%;
bottom: 75.5%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 54.5%;
right: 44.5%;
top: 22%;
bottom: 77%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 59.5%;
right: 39.5%;
top: 25.5%;
bottom: 73.5%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 64.5%;
right: 34.5%;
top: 23.5%;
bottom: 75.5%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 69.5%;
right: 29.5%;
top: 27%;
bottom: 72%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 27%;
right: 72%;
top: 74.5%;
bottom: 24.5%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 34.5%;
right: 64.5%;
top: 72%;
bottom: 27%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 42%;
right: 57%;
top: 76%;
bottom: 23%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 49.5%;
right: 49.5%;
top: 73%;
bottom: 26%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 57%;
right: 42%;
top: 75.5%;
bottom: 23.5%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 64.5%;
right: 34.5%;
top: 72%;
bottom: 27%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 72%;
right: 27%;
top: 74.5%;
bottom: 24.5%;

background: #0C4A6E;


/* Vector */

position: absolute;
left: 15%;
right: 15%;
top: 10%;
bottom: 10%;

border: 1.5px solid rgba(14, 165, 233, 0.6);


/* Highland Population Cohort Database */

position: absolute;
width: 176px;
height: 32px;
left: 127px;
top: 29.5px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 16px;
line-height: 20px;
/* or 125% */
display: flex;
align-items: center;

color: #000000;



/* Frame 2 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 60px;

width: 754px;
height: 19px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Home */

width: 45px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

color: #05121B;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Search */

width: 53px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Dataset */

width: 59px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 2 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 134px;
height: 19px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Analysis Tools */

width: 112px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 590;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Down */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 7.59%;
right: 7.59%;
top: 21.43%;
bottom: 21.43%;

/* 主文字颜色 */
background: rgba(0, 0, 0, 0.85);


/* Prediction Model */

width: 128px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Help */

width: 35px;
height: 19px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 19px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Button

按钮, btn, Кнопка
*/

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 15px;
gap: 8px;

display: none;
width: 70px;
height: 30px;

background: #07141D;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 10 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 20px 0px;
gap: 10px;

position: absolute;
width: 1440px;
height: 70px;
left: 0px;
bottom: 0px;

background: #FBFBFB;
border-top: 1px solid rgba(0, 0, 0, 0.12);


/* Copyright © 1998 - 2025 Tencent. All Rights Reserved.  Highland Population Cohort Database */

width: 710px;
height: 20px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 20px;
/* identical to box height, or 125% */
display: flex;
align-items: center;

color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Steps-Dot

点状步骤条, 
Шаги в стиле точки
*/

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;

position: absolute;
width: 880px;
height: 42px;
left: calc(50% - 880px/2);
top: 311px;



/* item 01 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 370px;
height: 42px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* track */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 0px 0px 65px;
gap: 4px;

width: 370px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* dot */

width: 10px;
height: 10px;

background: #1975FF;
border-radius: 8px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* tail */

width: 291px;
height: 0px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Vector 1 */

position: absolute;
height: 0px;
left: 0px;
right: -61px;
top: calc(50% - 0px/2);

/* Neutral/4 */
border: 3px solid #F0F0F0;


/* content */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 2px;

width: 140px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* title */

width: 140px;
height: 24px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */
text-align: center;

/* Character/Primary .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* description */

display: none;
width: 140px;
height: 44px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* or 157% */
text-align: center;

/* Character/Title .85 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* item 02 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 1px 0px 0px;
gap: 9px;

width: 370px;
height: 42px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* track */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 0px 0px 66px;
gap: 4px;

width: 370px;
height: 8px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* dot */

width: 8px;
height: 8px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 点 */

position: absolute;
width: 8px;
height: 8px;
left: 0px;
top: 0px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);
border-radius: 8px;


/* tail */

width: 292px;
height: 0px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Vector 1 */

position: absolute;
height: 0px;
left: 0px;
right: -61px;
top: calc(50% - 0px/2);

/* Neutral/4 */
border: 3px solid #F0F0F0;


/* content */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 2px;

width: 140px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* title */

width: 140px;
height: 24px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */
text-align: center;

/* 2级辅助文案 */
color: rgba(0, 0, 0, 0.65);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* description */

display: none;
width: 140px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
text-align: center;

/* Character / Secondary .45 */
color: rgba(0, 0, 0, 0.45);


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* item 03 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 1px 0px 0px;
gap: 9px;

width: 140px;
height: 42px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* track */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 0px 0px 66px;
gap: 4px;

width: 140px;
height: 8px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* dot */

width: 8px;
height: 8px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 点 */

position: absolute;
width: 8px;
height: 8px;
left: 0px;
top: 0px;

/* Character/Disabled & Placeholder .25 */
background: rgba(0, 0, 0, 0.25);
border-radius: 8px;


/* content */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 2px;

width: 140px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* title */

width: 140px;
height: 24px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */
text-align: center;

/* 2级辅助文案 */
color: rgba(0, 0, 0, 0.65);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* description */

display: none;
width: 140px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */
text-align: center;

/* Character / Secondary .45 */
color: rgba(0, 0, 0, 0.45);


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141835 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 24px 48px;
gap: 12px;

position: absolute;
width: 1440px;
height: 141px;
left: 0px;
top: 90px;

background: linear-gradient(90deg, #5D9FFF 0%, #B8DCFF 48%, #6BBBFF 100%);


/* Differential Expression Analysis */

width: 1344px;
height: 33px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 28px;
line-height: 33px;

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Introduction content Introduction contentIntroduction contentIntroduction contentIntroduction contentIntroduction contentIntroduction contentIntroduction contentIntroduction contentIntroduction content */

width: 1344px;
height: 48px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 20px;
line-height: 24px;

color: rgba(0, 0, 0, 0.65);


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141833 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 24px;

position: absolute;
width: 1344px;
height: 868px;
left: 48px;
top: 413px;



/* Frame 2053141818 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 24px;

width: 1344px;
height: 204px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141817 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 24px;

width: 1010px;
height: 204px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Feature Overview */

width: 1010px;
height: 29px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 24px;
line-height: 29px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Differential analysis helps compare the differences in phenotypes, genes, metabolites, and proteins after entering the plateau at different times. This powerful analytical tool provides valuable insights into biological variations across different time periods, enabling more comprehensive research outcomes. */

width: 1010px;
height: 151px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 20px;
line-height: 28px;
/* or 140% */

/* 2级辅助文案 */
color: rgba(0, 0, 0, 0.65);


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 1;


/* image 87 */

width: 254px;
height: 204px;

background: url(image.png);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 2053141832 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 24px;
gap: 24px;

width: 1344px;
height: 640px;

border: 1px solid rgba(0, 0, 0, 0.06);
border-radius: 8px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141830 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 825px;

width: 1296px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141819 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

width: 115px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* SampleData */

width: 115px;
height: 24px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 510;
font-size: 20px;
line-height: 24px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141829 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 16px;

width: 358px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 5px 12px;
gap: 4px;

width: 198px;
height: 32px;

background: #FFFFFF;
border: 1px solid #000000;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Download */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.26%;
right: 8.26%;
top: 10.69%;
bottom: 10.74%;

/* 主文字颜色 */
background: rgba(0, 0, 0, 0.85);


/* 文本备份 9 */

width: 154px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* button */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 5px 12px;
gap: 4px;

width: 144px;
height: 32px;

background: #1975FF;
box-shadow: 0px 2px 4px rgba(44, 78, 244, 0.25);
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Upload */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.26%;
right: 8.26%;
top: 10.74%;
bottom: 10.68%;

/* 白 */
background: #FFFFFF;


/* 文本备份 9 */

width: 100px;
height: 22px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
/* identical to box height, or 157% */

color: #FFFFFF;

text-shadow: 0px 1px 0px rgba(0, 17, 102, 0.148547);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 2053141831 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: flex-start;
padding: 0px;
gap: 67px;

width: 1296px;
height: 536px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 2053141826 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 350px;
height: 94px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 2053141820 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px;
gap: 6px;

width: 350px;
height: 40px;

background: linear-gradient(90deg, #F9FAFD 0%, #FFFFFF 100%);
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* File */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 10.72%;
right: 10.71%;
top: 0%;
bottom: 0%;

background: #3366FF;


/* File nameFile nameFile name.csv */

width: 216px;
height: 17px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 17px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 2053141821 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px;
gap: 6px;

width: 350px;
height: 42px;

background: linear-gradient(90deg, #F9FAFD 0%, #FFFFFF 100%);
border: 1px solid #3366FF;
box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* File */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 10.72%;
right: 10.71%;
top: 0%;
bottom: 0%;

background: #3366FF;


/* File nameFile nameFile name.csv */

width: 216px;
height: 17px;

font-family: 'SF Pro';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 17px;
/* identical to box height */

/* 主文字颜色 */
color: rgba(0, 0, 0, 0.85);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* image 88 */

width: 898px;
height: 536px;

background: url(image.png);
filter: drop-shadow(0px 0px 8px #EDF6FF);
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
