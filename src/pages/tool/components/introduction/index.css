/* Introduction 组件样式 */
.introduction-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 24px;
  width: 100%;
  font-family:
    'SF Pro',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;

  /* Feature Overview 区域 */
  .feature-overview-section {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0px;
    gap: 24px;
    width: 100%;
    height: 204px;

    .feature-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 0px;
      gap: 24px;
      width: 1010px;
      height: 204px;
      flex: none;
      order: 0;
      align-self: stretch;
      flex-grow: 0;

      .feature-title {
        width: 1010px;
        height: 29px;
        font-family: 'SF Pro';
        font-style: normal;
        font-weight: 510;
        font-size: 24px;
        line-height: 29px;
        color: rgba(0, 0, 0, 0.85);
        margin: 0;
        flex: none;
        order: 0;
        align-self: stretch;
        flex-grow: 0;
      }

      .feature-description {
        width: 1010px;
        height: 151px;
        font-family: 'SF Pro';
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.65);
        margin: 0;
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
      }
    }
  }

  .feature-title {
    width: 1010px;
    height: 29px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 510;
    font-size: 24px;
    line-height: 29px;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
    flex: none;
    order: 0;
    align-self: stretch;
    flex-grow: 0;
  }

  .feature-description {
    width: 1010px;
    height: 151px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 28px;
    color: rgba(0, 0, 0, 0.65);
    margin: 0;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 1;
  }

  .feature-image {
    width: 254px;
    height: 204px;
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  .overview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  /* SampleData 区域 */
  .sample-data-section {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 24px;
    gap: 24px;
    width: 100%;
    height: 640px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
  }

  .sample-data-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0px;
    gap: 825px;
    width: 100%;
    height: 32px;
    flex: none;
    order: 0;
    align-self: stretch;
    flex-grow: 0;
  }

  .sample-data-title-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 10px;
    width: 115px;
    height: 24px;
    flex: none;
    order: 0;
    flex-grow: 0;
  }

  .sample-data-title {
    width: 115px;
    height: 24px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 510;
    font-size: 20px;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
    flex: none;
    order: 0;
    flex-grow: 0;
  }

  .sample-data-actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 16px;
    width: 358px;
    height: 32px;
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  /* 按钮样式 */
  .download-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 5px 12px;
    gap: 4px;
    width: 198px;
    height: 32px;
    background: #ffffff;
    border: 1px solid #000000;
    border-radius: 4px;
    cursor: pointer;
    flex: none;
    order: 0;
    flex-grow: 0;
  }

  .download-button span {
    width: 154px;
    height: 22px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  .upload-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 5px 12px;
    gap: 4px;
    width: 144px;
    height: 32px;
    background: #1975ff;
    box-shadow: 0px 2px 4px rgba(44, 78, 244, 0.25);
    border-radius: 4px;
    border: none;
    cursor: pointer;
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  .upload-button span {
    width: 100px;
    height: 22px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #ffffff;
    text-shadow: 0px 1px 0px rgba(0, 17, 102, 0.148547);
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  .download-icon,
  .upload-icon {
    width: 16px;
    height: 16px;
    flex: none;
    order: 0;
    flex-grow: 0;
  }

  .download-icon {
    color: rgba(0, 0, 0, 0.85);
  }

  .upload-icon {
    color: #ffffff;
  }

  /* SampleData 内容区域 */
  .sample-data-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0px;
    gap: 67px;
    width: 100%;
    height: 536px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
  }

  .file-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0px;
    gap: 12px;
    height: 94px;
    flex: none;
    order: 0;
    flex-grow: 0;
  }

  .file-item {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px;
    gap: 6px;
    height: 40px;
    background: linear-gradient(90deg, #f9fafd 0%, #ffffff 100%);
    border-radius: 4px;
    flex: none;
    order: 0;
    align-self: stretch;
    flex-grow: 0;
  }

  .file-item.selected {
    height: 42px;
    border: 1px solid #3366ff;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
    order: 1;
  }

  .file-icon {
    width: 16px;
    height: 16px;
    flex: none;
    order: 0;
    flex-grow: 0;
  }

  .file-name {
    width: 216px;
    height: 17px;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: rgba(0, 0, 0, 0.85);
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  .data-table {
    flex: 1;
    height: 536px;
    min-width: 0;
    order: 1;
  }

  .table-container {
    width: 100%;
    height: 100%;
    background: #ffffff;
    filter: drop-shadow(0px 0px 8px #edf6ff);
    border-radius: 4px;
    overflow: auto;
  }

  .sample-table {
    width: 100%;
    border-collapse: collapse;
    font-family:
      'SF Pro',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
    font-size: 14px;
    border: 1px solid rgba(0, 0, 0, 0.06);
  }

  .sample-table th {
    background: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    padding: 12px 8px;
    text-align: center;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 510;
    font-size: 14px;
    line-height: 17px;
    color: rgba(0, 0, 0, 0.85);
    white-space: nowrap;
    min-width: 80px;
  }

  .sample-table th:last-child {
    border-right: none;
  }

  .sample-table td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    padding: 12px 8px;
    text-align: center;
    font-family: 'SF Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: rgba(0, 0, 0, 0.85);
    white-space: nowrap;
    min-width: 80px;
  }

  .sample-table td:last-child {
    border-right: none;
  }

  .sample-table tbody tr:hover td {
    background-color: rgba(0, 0, 0, 0.02);
  }

  /* 表格滚动条样式 */
  .table-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .table-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .table-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* 功能介绍区域 */
  .function-intro-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 24px 0;
    gap: 12px;
    width: 100%;
  }
}

.function-title {
  font-family: 'SF Pro';
  font-style: normal;
  font-weight: 510;
  font-size: 20px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
}

.function-description {
  font-family: 'SF Pro';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.65);
  margin: 0;
}

/* 按钮悬停效果 */
.download-button:hover {
  background: #f8f9fa;
  border-color: #333;
}

.upload-button:hover {
  background: #1664d9;
  box-shadow: 0px 2px 6px rgba(44, 78, 244, 0.35);
}
