import { useAnalysisToolSampleData } from '@/hooks/useToolQuery'
import { useDataPreview } from '@/hooks/useSearchQuery'
import './index.css'
import { useEffect, useState } from 'react'
import { Spin } from 'antd'

interface IntroductionProps {
  onUploadClick?: () => void
}

export default function Introduction({ onUploadClick }: IntroductionProps) {
  const [fileId, setFileId] = useState('')

  const searchParams = new URLSearchParams(location.search)
  const toolName = searchParams.get('toolName') ?? ''
  const outputType = searchParams.get('outputType') ?? ''
  const { data, isLoading } = useAnalysisToolSampleData({
    params: {
      toolName,
      outputType,
    },
  })
  const { data: previewData, isLoading: previewLoading } = useDataPreview({
    params: {
      id: fileId,
      module: 'tool',
    },
    disabled: !fileId,
  })
  console.log('previewData', previewData)

  useEffect(() => {
    if (data) {
      setFileId(data?.[0]?.id)
    }
  }, [data])

  if (isLoading || previewLoading) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <Spin />
      </div>
    )
  }

  return (
    <div className="introduction-container">
      {/* Feature Overview 区域 */}
      <div className="feature-overview-section">
        <div className="feature-content">
          <h2 className="feature-title">Feature Overview</h2>
          <p className="feature-description">
            Differential analysis helps compare the differences in phenotypes, genes, metabolites,
            and proteins after entering the plateau at different times. This powerful analytical
            tool provides valuable insights into biological variations across different time
            periods, enabling more comprehensive research outcomes.
          </p>
        </div>
        <div className="feature-image">
          <img
            src="/src/assets/tool/overview1.svg"
            alt="Feature Overview"
            className="overview-image"
          />
        </div>
      </div>

      {/* SampleData 区域 */}
      <div className="sample-data-section">
        <div className="sample-data-header">
          <div className="sample-data-title-wrapper">
            <h3 className="sample-data-title">SampleData</h3>
          </div>
          <div className="sample-data-actions">
            <button className="download-button">
              <svg className="download-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 10.5L5 7.5H7V3H9V7.5H11L8 10.5Z" fill="currentColor" />
                <path d="M3 12H13V13H3V12Z" fill="currentColor" />
              </svg>
              <span>Download Sample Data</span>
            </button>
            <button className="upload-button" onClick={onUploadClick}>
              <svg className="upload-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 5.5L11 8.5H9V13H7V8.5H5L8 5.5Z" fill="currentColor" />
                <path d="M3 3H13V4H3V3Z" fill="currentColor" />
              </svg>
              <span>upload my files</span>
            </button>
          </div>
        </div>

        <div className="sample-data-content">
          <div className="file-list">
            {data?.map((item: any) => (
              <div
                key={item.id}
                className={`file-item ${item.id === fileId ? 'selected' : ''}`}
                onClick={() => setFileId(item.id)}
              >
                <svg className="file-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M3 2H10L13 5V14H3V2Z" fill="#3366FF" />
                </svg>
                <span className="file-name">{item.fileName}</span>
              </div>
            ))}
          </div>
          <div className="data-table">
            {/* 数据表格 */}
            <div className="table-container">
              <table className="sample-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Bodyweight (kg)</th>
                    <th>Body fat (%)</th>
                    <th>Lean body mass(%)</th>
                    <th>SL</th>
                    <th>AL T1</th>
                    <th>AL T16</th>
                    <th>POST7</th>
                    <th>POST21</th>
                    <th>SL</th>
                    <th>AL T1</th>
                    <th>AL T16</th>
                    <th>SL</th>
                    <th>AL T1</th>
                    <th>AL T16</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>001</td>
                    <td>80.8</td>
                    <td>76.8</td>
                    <td>78.6</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>002</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>003</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>004</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>005</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>006</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>007</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>008</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>009</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>010</td>
                    <td>65.4</td>
                    <td>64.1</td>
                    <td>65</td>
                    <td>8.4</td>
                    <td>8.5</td>
                    <td>8.8</td>
                    <td>74</td>
                    <td></td>
                    <td>71.5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
