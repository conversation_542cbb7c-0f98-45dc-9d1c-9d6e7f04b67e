/* 工具页面样式 */
.tool-page {
  width: 100%;
  min-height: calc(100vh - 90px);
  background: #ffffff;
  font-family:
    'SF Pro',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;

  /* 蓝色渐变背景区域 */
  .hero-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 24px 48px;
    gap: 12px;
    width: 100%;
    height: 141px;
    background: linear-gradient(90deg, #5d9fff 0%, #b8dcff 48%, #6bbbff 100%);
    box-sizing: border-box;

    .hero-title {
      width: 100%;
      height: 33px;
      font-family: 'SF Pro';
      font-style: normal;
      font-weight: 510;
      font-size: 28px;
      line-height: 33px;
      color: rgba(0, 0, 0, 0.85);
      margin: 0;
      flex: none;
      order: 0;
      align-self: stretch;
      flex-grow: 0;
    }

    .hero-description {
      width: 100%;
      height: 48px;
      font-family: 'SF Pro';
      font-style: normal;
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.65);
      margin: 0;
      flex: none;
      order: 1;
      align-self: stretch;
      flex-grow: 0;
    }
  }

  /* 步骤导航容器 */
  .steps-container {
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 80px 0 60px;
  }

  /* Ant Design Steps 自定义样式 */
  .custom-steps {
    width: 80%;
    font-family:
      'SF Pro',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
  }

  /* .custom-steps .ant-steps-item {
  flex: 1;
}

.custom-steps .ant-steps-item:last-child {
  flex: none;
  width: 140px;
}

.custom-steps .ant-steps-item-icon {
  width: 10px;
  height: 10px;
  line-height: 10px;
  margin-left: 65px;
}

.custom-steps .ant-steps-item-process .ant-steps-item-icon {
  background: #1975ff;
  border-color: #1975ff;
  width: 10px;
  height: 10px;
}

.custom-steps .ant-steps-item-wait .ant-steps-item-icon {
  background: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  width: 8px;
  height: 8px;
}

.custom-steps .ant-steps-item-icon .ant-steps-icon {
  display: none;
}

.custom-steps .ant-steps-item-title {
  font-family: 'SF Pro';
  font-style: normal;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  width: 140px;
  margin-left: -70px;
}

.custom-steps .ant-steps-item-process .ant-steps-item-title {
  font-weight: 510;
  color: rgba(0, 0, 0, 0.85);
}

.custom-steps .ant-steps-item-wait .ant-steps-item-title {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
}

.custom-steps .ant-steps-item-tail::after {
  background-color: #f0f0f0;
  height: 3px;
} */

  /* 内容容器 */
  .content-container {
    width: 100%;
    padding: 0 48px 48px;
    box-sizing: border-box;
  }
}
