<svg width="167" height="48" viewBox="0 0 167 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="125" y="6" width="52" height="52"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7px);clip-path:url(#bgblur_0_183_10850_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_d_183_10850)" data-figma-bg-blur-radius="14">
<circle cx="12" cy="12" r="12" transform="matrix(-1 0 0 1 163 20)" fill="white" fill-opacity="0.4" shape-rendering="crispEdges"/>
</g>
<circle cx="5" cy="5" r="5" transform="matrix(-1 0 0 1 156 27)" fill="white"/>
<circle cx="5" cy="5" r="5" transform="matrix(-1 0 0 1 10 0)" fill="white"/>
<path d="M149 32H66.6711L8 5" stroke="white" stroke-width="2"/>
<defs>
<filter id="filter0_d_183_10850" x="125" y="6" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_183_10850"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_183_10850" result="shape"/>
</filter>
<clipPath id="bgblur_0_183_10850_clip_path" transform="translate(-125 -6)"><circle cx="12" cy="12" r="12" transform="matrix(-1 0 0 1 163 20)"/>
</clipPath></defs>
</svg>
