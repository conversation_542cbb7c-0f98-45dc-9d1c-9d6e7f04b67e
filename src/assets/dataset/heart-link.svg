<svg width="245" height="98" viewBox="0 0 245 98" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-10" y="56" width="52" height="52"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7px);clip-path:url(#bgblur_0_183_10918_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_d_183_10918)" data-figma-bg-blur-radius="14">
<circle cx="16" cy="82" r="12" fill="white" fill-opacity="0.4" shape-rendering="crispEdges"/>
</g>
<circle cx="16" cy="82" r="5" fill="white"/>
<circle cx="240" cy="5" r="5" fill="white"/>
<path d="M16 82H143.872L238.5 5" stroke="white" stroke-width="2"/>
<defs>
<filter id="filter0_d_183_10918" x="-10" y="56" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_183_10918"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_183_10918" result="shape"/>
</filter>
<clipPath id="bgblur_0_183_10918_clip_path" transform="translate(10 -56)"><circle cx="16" cy="82" r="12"/>
</clipPath></defs>
</svg>
