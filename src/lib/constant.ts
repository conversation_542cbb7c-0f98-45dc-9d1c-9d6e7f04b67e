export const TOOLS = {
  differentialExpressionAnalysis: {
    title: 'Differential Expression Analysis',
    value: 'differentialExpressionAnalysis',
    output: {
      heatMap: {
        title: 'Heat Map',
        value: 'heatMap',
      },
      correlationMatrix: {
        title: 'Correlation Matrix',
        value: 'correlationMatrix',
      },
      scatterDiagram: {
        title: 'Scatter Diagram',
        value: 'scatterDiagram',
      },
      boxPlots: {
        title: 'Box Plots',
        value: 'boxPlots',
      },
      cluster: {
        title: 'Cluster',
        value: 'cluster',
      },
    },
  },
  phenotypeOmicsAssociationAnalysis: {
    title: 'Phenotype-Omics Association Analysis',
    value: 'phenotypeOmicsAssociationAnalysis',
    output: {
      heatMap: {
        title: 'Heat Map',
        value: 'heatMap',
      },
      correlationMatrix: {
        title: 'Correlation Matrix',
        value: 'correlationMatrix',
      },
      scatterDiagram: {
        title: 'Scatter Diagram',
        value: 'scatterDiagram',
      },
      sankeyDiagram: {
        title: 'Sankey Diagram',
        value: 'sankeyDiagram',
      },
      nineQuadrantDiagram: {
        title: 'Nine-Quadrant Diagram',
        value: 'nineQuadrantDiagram',
      },
    },
    pathwayEnrichmentAnalysis: {
      title: 'Pathway Enrichment Analysis',
      value: 'pathwayEnrichmentAnalysis',
      output: {
        bubbleDiagram: {
          title: 'Bubble Diagram',
          value: 'bubbleDiagram',
        },
        barPlot: {
          title: 'Bar plot',
          value: 'barPlot',
        },
        goKeggAssociationNetwork: {
          title: 'GO/KEGG Association Network',
          value: 'goKeggAssociationNetwork',
        },
        chordDiagram: {
          title: 'Chord Diagram',
          value: 'chordDiagram',
        },
        chordTableHybrid: {
          title: 'Chord-Table Hybrid',
          value: 'chordTableHybrid',
        },
      },
    },
    coExpressionNetworkAnalysis: {
      title: 'Co-Expression Network Analysis',
      value: 'coExpressionNetworkAnalysis',
      output: {
        clusteringTree: {
          title: 'Clustering Tree',
          value: 'clusteringTree',
        },
        correlationHeatMap: {
          title: 'Correlation Heat Map',
          value: 'correlationHeatMap',
        },
        coExpressionNetwork: {
          title: 'Co-Expression Network',
          value: 'coExpressionNetwork',
        },
        scatterDiagram: {
          title: 'Scatter Diagram',
          value: 'scatterDiagram',
        },
        weightingNetwork: {
          title: 'Weighting Network',
          value: 'weightingNetwork',
        },
      },
    },
  },
}
