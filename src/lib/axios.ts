import axios from 'axios'
import { message } from 'antd'

const instance = axios.create({
  // baseURL: "/api", // 可根据环境配置
  baseURL: import.meta.env.VITE_API_BASE_URL,
  withCredentials: true, // 全局启用 Cookie 携带
  timeout: 10000,
  paramsSerializer: {
    indexes: null, // no brackets at all
  },
})

axios.defaults.withCredentials = true
instance.defaults.withCredentials = true

// 刷新 token 的锁和等待队列
let isRefreshing = false
let subscribers: ((token: string) => void)[] = []

function onRefreshed(token: string) {
  subscribers.forEach(cb => cb(token))
  subscribers = []
}

function refreshTokenRequest() {
  const refreshToken = localStorage.getItem('refreshToken')
  // 用原生 axios，避免再次进入拦截器死循环
  return axios.post('/api/auth/refresh', { refreshToken })
}

instance.interceptors.request.use(config => {
  // const token = localStorage.getItem('token');
  // config.headers['X-UserInfo'] = 'eyJnaXZlbl9uYW1lIjoi5LiK5rW356eR5a2m5pm66IO956CU56m26ZmiIiwic3ViIjoiNTgxMjlkNGItYjk4MC00M2M4LWEyNDUtZWI4ZWM2NjNiNzU1IiwicHJlZmVycmVkX3VzZXJuYW1lIjoiMTMwNTY1NzEwMzIiLCJlbWFpbF92ZXJpZmllZCI6ZmFsc2UsIm5hbWUiOiLkuIrmtbfnp5Hlrabmmbrog73noJTnqbbpmaIifQ=='  // TODO: temp test
  // if (token) {
  //   config.headers.Authorization = `Bearer ${token}`;
  // }
  return config
})

instance.interceptors.response.use(
  response => {
    const res = response.data
    // 如果是自定义业务错误
    if (res?.code !== 200) {
      if (res?.code === 401) {
        if (!isRefreshing) {
          isRefreshing = true
          refreshTokenRequest()
            .then(res => {
              const data = res.data
              if (data?.code === 200 && data?.data?.token) {
                window.dispatchEvent(
                  new CustomEvent('tokenUpdated', { detail: { token: data.data.token } }),
                )
                // localStorage.setItem('refreshToken', data.data.refreshToken)
                onRefreshed(data.data.token)
                return data.data.token
              } else {
                throw new Error('刷新 token 失败')
              }
            })
            .catch(e => {
              localStorage.removeItem('token')
              localStorage.removeItem('refreshToken')
              location.href = '/login'
              // 只有刷新失败时才提示
              message.warning('登录状态已失效，请重新登录')
              return Promise.reject(e)
            })
            .finally(() => {
              isRefreshing = false
            })
        }
        // 401场景下不弹toast，直接reject
        return Promise.reject(res)
      }
      // 其它业务错误才弹toast
      message.warning(res.message || '操作失败')
      return Promise.reject(res)
    }
    // 成功则只返回数据部分
    return res.data
  },
  async err => {
    console.error('API error:', err)
    const message = err?.response?.data?.message || err?.message || '服务器错误'
    message.error(message)
    return Promise.reject(err)
  },
)

export default instance
