import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { v4 as uuidv4 } from 'uuid'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 转换函数
export function convertToColumns(header: any) {
  return header.map((group:any) => {
    let children = [];
    if (group.children) {
      // 处理子列
      children = group.children.map((child:any) => {
        const index = child.columnIndex;
        return {
          title: child.title,
          key: uuidv4(),
          render: (text:any) => text[index] ?? ''
        };
      });
    }
    return {
      title: group.title,
      children: children,
      key: uuidv4(),
      render: (text:any) => (group.columnIndex !== undefined && group.columnIndex !== null)?(text[group.columnIndex]||''): ''
    };
  });
}