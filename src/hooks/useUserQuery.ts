import { useGet, usePost, usePut, useDelete } from '@/lib/request'
import {
  UserResponse,
  RegisterResponse,
  RegisterRequest,
  UpdateUserRequest,
  LoginRequest,
  LoginResponse,
  CreateUserResponse,
  CreateUserRequest,
  UpdatePasswordRequest,
} from '@/types/user'

export const useRegister = (options?: any) => {
  return usePost<RegisterResponse, RegisterRequest>(`/auth/register`, options)
}

export const useCreateUser = (options?: any) => {
  return usePost<CreateUserResponse, CreateUserRequest>(`/users`, options)
}

export const useQueryUser = (options?: any) => {
  return useGet<UserResponse>(`/users`, options)
}

export const useQueryUsers = (options?: any) => {
  return useGet<UserResponse[]>(`/users`, options)
}

export const useUpdateUser = (options?: any) => {
  return usePut<UserResponse, UpdateUserRequest>(`/users`, options)
}

export const useDeleteUser = (options?: any) => {
  return useDelete<UserResponse, number>(`/users`, options)
}

export const useLogin = (options?: any) => {
  return usePost<LoginResponse, LoginRequest>(`/auth/login`, options)
}

export const useRefreshToken = (options?: any) => {
  return usePost<LoginResponse, string>(`/auth/refresh`, options)
}

export const useLogout = (options?: any) => {
  return usePost<LoginResponse, void>(`/auth/logout`, options)
}

export const useResetPassword = (options?: any) => {
  return usePost<LoginResponse, { email: string }>(`/auth/reset`, options)
}

export const useUpdatePassword = (options?: any) => {
  return usePut<UserResponse, UpdatePasswordRequest>(`/auth/update/password`, options)
}

export const useSearchData = (options?: any) => {
  return useGet<UserResponse>(`/data/classification`, options)
}

export const useSearchTableData = (options?: any) => {
  return useGet<UserResponse>(`/data/preview`, options)
}
