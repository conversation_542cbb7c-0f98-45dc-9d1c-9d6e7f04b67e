import { useGet } from '@/lib/request'
import { MetaInfoResponse, DetailInfoParams, DetailInfoResponse } from '@/types/dataset'

export const useDatasetMetaInfo = (options?: any) => {
  return useGet<MetaInfoResponse>('/data/datasetMetaInfo', options)
}

export const useDatasetDetailInfo = (options?: any) => {
  return useGet<DetailInfoResponse, DetailInfoParams>('/data/datasetDetailInfo', options)
}
