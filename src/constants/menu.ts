import { Role } from '@/types/user'

export interface MenuItem {
  label: string
  path: string
  roles?: Role[]
  children?: MenuItem[]
}

export const menuList: MenuItem[] = [
  {
    label: 'Home',
    path: '/',
    roles: ['admin', 'user'],
  },
  {
    label: "Search",
    path: "/search",
    roles: ["admin", "user"],
  },
  {
    label: "Dataset",
    path: "/dataset",
    roles: ["admin", "user"],
  },
  {
    label: 'Analysis tool',
    path: '/tool',
    roles: ['admin', 'user'],
    children: [
      {
        label: 'Analysis',
        path: '/tool/analysis',
      },
      {
        label: 'result',
        path: '/tool/result',
      },
    ],
  },
  {
    label: "Prediction Model",
    path: "/model",
    roles: ["admin", "user"],
  },
  {
    label: "Help",
    path: "/help",
    roles: ["admin", "user"],
  },
];