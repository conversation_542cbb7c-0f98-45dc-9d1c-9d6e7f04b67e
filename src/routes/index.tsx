import { useRoutes } from 'react-router-dom'
import { lazy } from 'react'
import AppLayout from '@/layouts/AppLayout'
import AuthLayout from '@/layouts/AuthLayout'
import ResetPasswordPage from '@/pages/login/reset'
import DataSetPage from '@/pages/dataset'
import SearchPage from '@/pages/home/<USER>/Search'
import DatasetDetailPage from '@/pages/dataset-detail'
import DataPrepDemo from '@/pages/data-prep-demo'
import ReportDownloadDemo from '@/pages/report-download-demo'

const Login = lazy(() => import('@/pages/login'))
const HomePage = lazy(() => import('@/pages/home'))
const ToolPage = lazy(() => import('@/pages/tool'))
const Tool1Page = lazy(() => import('@/pages/tool/tool1'))
const Tool2Page = lazy(() => import('@/pages/tool/tool2'))
const NotFound = lazy(() => import('@/pages/not-found'))

export const AppRoutes = () =>
  useRoutes([
    // 认证相关路由（公开访问）
    {
      path: '/login',
      element: <AuthLayout />,
      children: [{ index: true, element: <Login /> }],
    },
    {
      path: '/reset',
      element: <AuthLayout />,
      children: [{ index: true, element: <ResetPasswordPage /> }],
    },

    // 主应用路由
    {
      path: '/',
      element: <AppLayout />,
      children: [
        {
          index: true,
          element: <HomePage />,
        },
        {
          path: 'search',
          element: <SearchPage />,
        },
        {
          path: 'dataset',
          element: <DataSetPage />,
        },
        {
          path: 'dataset-detail',
          element: <DatasetDetailPage />,
        },
        {
          path: 'data-prep-demo',
          element: <DataPrepDemo />,
        },
        {
          path: 'report-download-demo',
          element: <ReportDownloadDemo />,
        },
        {
          path: 'tool',
          children: [
            {
              index: true,
              element: <ToolPage />,
            },
            {
              path: 'analysis',
              element: <Tool1Page />,
            },
            {
              path: 'result',
              element: <Tool2Page />,
            },
          ],
        },
      ],
    },

    // 404 页面（公开访问，不需要认证）
    {
      path: '*',
      element: <AppLayout />,
      children: [{ path: '*', element: <NotFound /> }],
    },
  ])
