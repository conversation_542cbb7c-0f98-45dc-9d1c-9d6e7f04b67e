import { useEffect, useRef } from 'react'
import * as <PERSON>CL from 'openchemlib'
import * as $3Dmol from '3dmol'

export default function MoleculeViewer({ smiles = '' }: { smiles?: string }) {
  const svgRef = useRef<HTMLDivElement>(null)
  const viewerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!smiles) {
      return
    }

    try {
      // 生成 2D 结构
      const molecule = OCL.Molecule.fromSmiles(smiles)
      molecule.addImplicitHydrogens()

      const svg = molecule.toSVG(300, 300, 'white')

      if (svgRef.current) {
        svgRef.current.innerHTML = svg || '<p>无法渲染 SVG</p>'
      } else {
        console.warn('[SVG] svgRef.current 为空')
      }

      // 生成 3D 结构
      const molfile = molecule.toMolfile()

      if (viewerRef.current) {
        const viewer = $3Dmol.createViewer(viewerRef.current, {
          backgroundColor: 'white',
        })
        viewer.addModel(molfile, 'sdf')
        viewer.setStyle({}, { stick: {}, sphere: { scale: 0.3 } })
        viewer.zoomTo()
        viewer.render()
      } else {
        console.warn('[3Dmol] viewerRef.current 为空')
      }
    } catch (err) {
      console.error('[错误] 分子渲染失败：', err)
    }
  }, [smiles])

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      <div>
        <h3>2D Structure</h3>
        <div
          ref={svgRef}
          style={{
            width: '300px',
            height: '300px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        />
      </div>
      <div>
        <h3>3D Structure</h3>
        <div
          ref={viewerRef}
          style={{
            width: '300px',
            height: '300px',
            position: 'relative',
          }}
        />
      </div>
    </div>
  )
}
