import { useEffect, useState, useCallback } from 'react'
import { Image, message, Spin } from 'antd'
import './niiViewer.css'

interface MedicalNiftiViewerProps {
  filePath: string
  isFullscreen?: boolean
}

const BASE_URL = import.meta.env.VITE_BASE_NII_URL
const AXES = ['x', 'y', 'z']

const NiiViewer = ({ filePath, isFullscreen }: MedicalNiftiViewerProps) => {
  const [shape, setShape] = useState<number[]>([0, 0, 0])
  const [positions, setPositions] = useState<Record<string, number>>({ x: 0, y: 0, z: 0 })
  const [brightness, setBrightness] = useState(1.0)
  const [contrast, setContrast] = useState(1.0)
  const [rotationAngle, setRotationAngle] = useState<number>(0)
  const [slices, setSlices] = useState<Record<string, string>>({ x: '', y: '', z: '' })
  const [slicesLoading, setSlicesLoading] = useState<Record<string, boolean>>({
    x: false,
    y: false,
    z: false,
  })
  const [fullWidth, setFullWidth] = useState(isFullscreen ? window.innerWidth : 608)
  const [isLoading, setIsLoading] = useState(true)
  const [messageApi, contextHolder] = message.useMessage()

  // 更新单个切片
  const updateSingleSlice = useCallback(
    async (axis: string, position: number, brightness: number, contrast: number) => {
      try {
        setSlices(prev => ({
          ...prev,
          [axis]: '',
        }))
        setSlicesLoading(prev => ({
          ...prev,
          [axis]: true,
        }))
        const response = await fetch(
          `${BASE_URL}/get_slice?axis=${axis}&index=${position}&brightness=${brightness}&contrast=${contrast}`,
        )
        if (!response.ok) {
          throw new Error('Failed to fetch slice')
        }
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)

        setSlices(prev => ({
          ...prev,
          [axis]: url,
        }))
        setSlicesLoading(prev => ({
          ...prev,
          [axis]: false,
        }))
      } catch (error) {
        messageApi.error(`Error fetching ${axis} slice: ${error}`)
        setSlicesLoading(prev => ({
          ...prev,
          [axis]: false,
        }))
      }
    },
    [],
  )

  // 更新所有切片
  const updateAllSlices = useCallback(
    async (positions: Record<string, number>, brightness: number, contrast: number) => {
      try {
        setSlices({ x: '', y: '', z: '' })

        const promises = AXES.map(axis =>
          updateSingleSlice(axis, positions[axis], brightness, contrast),
        )
        await Promise.all(promises)
      } catch (error) {
        messageApi.error(`Error updating all slices: ${error}`)
      }
    },
    [updateSingleSlice],
  )

  // 处理单个轴的位置变化
  const handlePositionChange = (axis: string, newPosition: number) => {
    setPositions(prev => ({
      ...prev,
      [axis]: newPosition,
    }))
  }

  // 处理亮度或对比度变化
  const handleControlChange = (control: 'brightness' | 'contrast', value: number) => {
    if (control === 'brightness') {
      setBrightness(value)
    } else {
      setContrast(value)
    }
  }

  // 初始化处理
  const processFiles = async () => {
    try {
      setSlices({ x: '', y: '', z: '' })
      setIsLoading(true)

      const response = await fetch(`${BASE_URL}/process_files?nifti_file_path=${filePath}`)
      const data = await response.json()
      if (!data?.shape) {
        messageApi.error('文件不存在。')
        return
      }

      setShape(data.shape)
      const newPositions = {
        x: Math.floor(data.shape[0] / 2),
        y: Math.floor(data.shape[1] / 2),
        z: Math.floor(data.shape[2] / 2),
      }
      setPositions(newPositions)

      await updateAllSlices(newPositions, brightness, contrast)
    } catch (error) {
      messageApi.error('文件请求失败。')
      console.error('Error fetching shape:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    processFiles()
  }, [filePath])

  useEffect(() => {
    if (isFullscreen) {
      setFullWidth(window.innerWidth)
    } else {
      setFullWidth(608)
    }
  }, [isFullscreen])

  const rotateAllImages = () => {
    setRotationAngle((rotationAngle + 90) % 360)
  }

  useEffect(() => {
    const handleResize = () => setFullWidth(window.innerWidth)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const calculateImageWidth = (idx: number) => {
    const relWidth = fullWidth - 48
    const imageFullWidth = relWidth - 32 - 40
    const imageHalfWidth = (relWidth - 32 * 2 - 60) / 2
    const imageThreeWidth = (relWidth - 32 * 3 - 80) / 3
    return isFullscreen ? imageThreeWidth : idx < 2 ? imageHalfWidth : imageFullWidth
  }

  return (
    <div className="NiiViewerContainer">
      {contextHolder}
      <div className="NiiControls">
        {['brightness', 'contrast'].map(control => (
          <div className="NiiControl" key={control}>
            <div className="NiiControlLabel">
              <label>{control.charAt(0).toUpperCase() + control.slice(1)}:</label>
            </div>
            <input
              type="range"
              min="0.5"
              max="2.0"
              step="0.1"
              value={control === 'brightness' ? brightness : contrast}
              onChange={e => {
                const newValue = Number.parseFloat(e.target.value)
                handleControlChange(control as 'brightness' | 'contrast', newValue)
              }}
              onMouseUp={() => updateAllSlices(positions, brightness, contrast)}
            />
          </div>
        ))}
        <button onClick={rotateAllImages}>Rotate 90°</button>
      </div>

      <div className="NiiSliceViewRow">
        {AXES.map((axis, idx) => (
          <div className="NiiSliceView" key={axis}>
            {isLoading ? (
              <div
                className="NiiSliceViewLoading"
                style={{
                  width: calculateImageWidth(idx),
                  height: calculateImageWidth(idx),
                }}
              >
                <Spin />
              </div>
            ) : slicesLoading[axis] ? (
              <div
                className="NiiSliceViewLoading"
                style={{
                  width: calculateImageWidth(idx),
                  height: calculateImageWidth(idx),
                }}
              >
                <Spin />
              </div>
            ) : slices[axis] ? (
              <Image
                alt={`${axis} slice`}
                width={calculateImageWidth(idx)}
                height={calculateImageWidth(idx)}
                style={{
                  objectFit: 'contain',
                  transform: `rotate(${rotationAngle}deg)`,
                }}
                src={slices[axis]}
              />
            ) : (
              <div
                className="NiiSliceViewEmpty"
                style={{
                  width: calculateImageWidth(idx),
                  height: calculateImageWidth(idx),
                }}
              >
                无内容
              </div>
            )}

            <input
              type="range"
              min="0"
              max={shape[idx] - 1}
              value={positions[axis]}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                handlePositionChange(axis, Number.parseInt(e.target.value, 10))
              }
              onMouseUp={() => updateSingleSlice(axis, positions[axis], brightness, contrast)}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

export default NiiViewer
