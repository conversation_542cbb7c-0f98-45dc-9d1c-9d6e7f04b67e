import { Table, Popover } from 'antd'
import { useEffect, useState } from 'react'
import { ColumnsType } from 'antd/es/table'
import MolecularViewer from './molecularViewer'

const CSVViewer = ({ content }: { content: string[] }) => {
  const [tableHeight, setTableHeight] = useState(0)

  useEffect(() => {
    const table = document.querySelector('.ant-table-content') as HTMLTableElement
    if (table) {
      setTableHeight(table.offsetHeight)
    }
  }, [])

  // 解析CSV行为列数据
  const parseCSV = (content: string[]): { columns: ColumnsType<any>; data: any[] } => {
    if (!content || content.length === 0) return { columns: [], data: [] }

    // 解析CSV行，处理带引号的字段
    const parseCSVLine = (line: string): string[] => {
      const result: string[] = []
      let current = ''
      let inQuotes = false

      for (let i = 0; i < line.length; i++) {
        const char = line[i]

        if (char === '"') {
          if (line[i + 1] === '"') {
            // 处理双引号转义
            current += '"'
            i++ // 跳过下一个引号
          } else {
            inQuotes = !inQuotes
          }
        } else if (char === ',' && !inQuotes) {
          result.push(current)
          current = ''
        } else {
          current += char
        }
      }

      // 添加最后一个字段
      result.push(current)
      return result.map(cell => cell.trim())
    }

    // 解析表头
    const headerLine = content[0] || ''
    const headers = parseCSVLine(headerLine)

    // 生成列配置
    const columns: ColumnsType<any> = headers.map((header, index) => ({
      title: header || `Column ${index + 1}`,
      dataIndex: `col_${index}`,
      key: `col_${index}`,
      ellipsis: true,
      render: (text: string) => {
        if (header?.toLowerCase() === 'smiles' && text) {
          return (
            <Popover
              title={<div className="max-w-[300px] truncate">{text}</div>}
              content={
                <div style={{ width: '300px', height: '700px' }}>
                  <MolecularViewer smiles={text} />
                </div>
              }
              overlayStyle={{ maxWidth: 'none' }}
            >
              <div style={{ cursor: 'pointer', color: '#1890ff' }}>{text}</div>
            </Popover>
          )
        }
        return text || '-'
      },
    }))

    // 解析数据行
    const data = content.slice(1).map((line, rowIndex) => {
      const row: any = { key: rowIndex }
      const values = parseCSVLine(line)

      values.forEach((value, colIndex) => {
        row[`col_${colIndex}`] = value
      })

      return row
    })

    return { columns, data }
  }

  const { columns, data } = parseCSV(content)

  return (
    <div className="flex flex-col" style={{ height: '100%' }}>
      <div className="flex-1 overflow-hidden">
        <Table
          size="small"
          bordered
          virtual
          scroll={{
            x: 2000,
            y: tableHeight,
          }}
          // components={{
          //   body: {
          //     wrapper: ({ style, ...props }: any) => (
          //       <div
          //         {...props}
          //         style={{
          //           ...style,
          //           maxHeight: 'none !important',
          //           height: '100%'
          //         }}
          //       />
          //     ),
          //   },
          // }}
          columns={columns}
          dataSource={data}
          pagination={false}
          rowClassName={(_, index) => (index % 2 === 0 ? 'bg-white' : 'bg-gray-50')}
          rowKey="key"
          onRow={record => ({
            onMouseEnter: event => {
              ;(event.currentTarget as HTMLElement).style.backgroundColor = '#f5f5f5'
            },
            onMouseLeave: event => {
              const index = data.findIndex(item => item.key === record.key)
              ;(event.currentTarget as HTMLElement).style.backgroundColor =
                index % 2 === 0 ? '#fff' : '#fafafa'
            },
          })}
        />
      </div>
    </div>
  )
}

export default CSVViewer
