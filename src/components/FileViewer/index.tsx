import Loading from '@/components/Loading'
import Empty from '@/components/Empty'
import React, { useEffect, useState, useCallback } from 'react'
import { fileTypes, getFileTypeExts, getExtension } from './util'
import CSVViewer from './csvViewer'
import ContentViewer from './contentViewer'
import NiiViewer from './niiViewer'
import MolecularViewer from './molecularViewer'
// import NotSupportedComponent from '../NotSupported';

// AutoSizer 子组件属性
// 移除未使用的类型定义

type FileViewerProps = {
  fileUrl: string
  filePath?: string
  isFullscreen?: boolean
}

const isImageFile = (ext: string) => getFileTypeExts(fileTypes.image)?.includes(ext)
const isAudioFile = (ext: string) => getFileTypeExts(fileTypes.audio)?.includes(ext)
const isCsvFile = (ext: string) => getFileTypeExts(fileTypes.csv)?.includes(ext)
const isPdfFile = (ext: string) => getFileTypeExts(fileTypes.pdf)?.includes(ext)
const isCodeFile = (ext: string) => getFileTypeExts(fileTypes.code)?.includes(ext)
const isMolecularFile = (ext: string) => getFileTypeExts(fileTypes.molecular)?.includes(ext)
const isNiiFile = (ext: string) => getFileTypeExts(fileTypes.nii)?.includes(ext)

const FileViewer: React.FC<FileViewerProps> = ({ fileUrl, filePath, isFullscreen }) => {
  const [content, setContent] = useState<string[]>([])
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  // 文件加载状态管理
  const [fileStates, setFileStates] = useState<{
    [key: string]: {
      loading: boolean
      error: boolean
      errorMessage?: string
    }
  }>({})

  // 更新文件状态
  const updateFileState = useCallback(
    (url: string, state: { loading?: boolean; error?: boolean; errorMessage?: string }) => {
      setFileStates(prev => ({
        ...prev,
        [url]: {
          ...prev[url],
          loading: state.loading ?? prev[url]?.loading ?? false,
          error: state.error ?? prev[url]?.error ?? false,
          errorMessage: state.errorMessage ?? prev[url]?.errorMessage,
        },
      }))
    },
    [],
  )
  const ext = getExtension(fileUrl)

  // 处理文件内容，按行分割
  const processContent = useCallback((text: string): string[] => {
    return text.split(/\r?\n/)
  }, [])

  const directFileTypes = [fileTypes.image, fileTypes.audio, fileTypes.pdf, fileTypes.nii]

  // 初始化文件加载状态
  useEffect(() => {
    if (!fileUrl) return

    // 如果是直接通过Nginx获取的文件类型
    if (directFileTypes.includes(ext)) {
      updateFileState(fileUrl, { loading: true, error: false })
    }
  }, [fileUrl, ext, updateFileState])

  // 获取文件大小
  useEffect(() => {
    if (!fileUrl) return

    const controller = new AbortController()
    const signal = controller.signal

    // 如果不是图片文件、视频文件、音频文件、PDF文件、Excel文件、CSV文件，则加载内容
    const loadContent = async () => {
      if (directFileTypes.includes(ext)) {
        return
      }

      setLoading(true)
      setError(null)
      setContent([])

      try {
        // const shouldLoad = await checkFileSize();
        // if (!shouldLoad) return;

        const response = await fetch(fileUrl, { signal })
        if (!response.ok) throw new Error('文件加载失败')

        const text = await response.text()
        const lines = processContent(text)
        setContent(lines)
      } catch (error) {
        const err = error as Error
        if (err.name !== 'AbortError') {
          setError(err.message || '文件加载失败')
        }
      } finally {
        setLoading(false)
      }
    }

    loadContent()

    return () => {
      controller.abort()
    }
  }, [fileUrl, ext])

  if (!fileUrl) return <Empty description="文件路径为空" />

  // 渲染直接通过Nginx获取的文件
  const renderDirectFile = (content: React.ReactNode) => {
    const currentState = fileStates[fileUrl] || { loading: true, error: false }

    return (
      <div className="relative w-full h-full flex items-center justify-center">
        {currentState.loading && (
          <div className="absolute inset-0 z-10">
            <Loading />
          </div>
        )}

        {!currentState.loading && currentState.error && (
          <div className="absolute inset-0 z-10">
            <Empty description={currentState?.errorMessage || '加载失败'} />
          </div>
        )}

        <div
          className={`w-full h-full flex items-start justify-center ${currentState.loading || currentState.error ? 'invisible' : 'visible'}`}
        >
          {content}
        </div>
      </div>
    )
  }

  if (isImageFile(ext)) {
    return renderDirectFile(
      <img
        src={fileUrl}
        alt="Image Preview"
        className="object-contain"
        loading="lazy"
        onLoad={() => updateFileState(fileUrl, { loading: false, error: false })}
        onError={() =>
          updateFileState(fileUrl, {
            loading: false,
            error: true,
            errorMessage: '图片加载失败',
          })
        }
      />,
    )
  }

  // if (isVideoFile(ext)) {
  //   return renderDirectFile(
  //     <video
  //       controls
  //       className="w-full h-full"
  //       onLoadedData={() => updateFileState(fileUrl, { loading: false, error: false })}
  //       onError={() => updateFileState(fileUrl, {
  //         loading: false,
  //         error: true,
  //         errorMessage: '视频加载失败'
  //       })}
  //     >
  //       <source src={fileUrl} type={`video/${ext}`} />
  //       您的浏览器不支持视频播放
  //     </video>
  //   );
  // }

  if (isAudioFile(ext)) {
    return renderDirectFile(
      <audio
        controls
        className="w-full h-full"
        onLoadedData={() => updateFileState(fileUrl, { loading: false, error: false })}
        onError={() =>
          updateFileState(fileUrl, {
            loading: false,
            error: true,
            errorMessage: '音频加载失败',
          })
        }
      >
        <source src={fileUrl} type={`audio/${ext}`} />
        您的浏览器不支持音频播放
      </audio>,
    )
  }

  if (isPdfFile(ext)) {
    return renderDirectFile(
      <iframe
        src={fileUrl}
        className="w-full h-full border-0"
        title="PDF 预览"
        onLoad={() => updateFileState(fileUrl, { loading: false, error: false })}
        onError={() =>
          updateFileState(fileUrl, {
            loading: false,
            error: true,
            errorMessage: 'PDF 加载失败',
          })
        }
      />,
    )
  }

  // if (isWordFile(ext) || isExcelFile(ext) || isPptFile(ext)) {
  //   return <NotSupportedComponent description="暂时不支持预览Office文件" />;
  //   // const officeViewerUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrl)}`;
  //   // return renderDirectFile(
  //   //   <iframe
  //   //     src={officeViewerUrl}
  //   //     className="w-full h-full border-0"
  //   //     title="Office 文档预览"
  //   //     onLoad={() => updateFileState(fileUrl, { loading: false, error: false })}
  //   //     onError={() => updateFileState(fileUrl, {
  //   //       loading: false,
  //   //       error: true,
  //   //       errorMessage: '文档加载失败'
  //   //     })}
  //   //   />
  //   // );
  // }

  // nii文件
  if (isNiiFile(ext)) {
    return <NiiViewer filePath={filePath || ''} isFullscreen={isFullscreen} />
  }

  if (loading) return <Loading />
  if (error) return <Empty description={error} />
  if (!content || content.length === 0) return <Empty description="文件内容为空" />

  // 渲染虚拟滚动内容
  const renderContent = () => {
    if (isCsvFile(ext)) {
      return <CSVViewer content={content} />
    }

    if (isMolecularFile(ext)) {
      return <MolecularViewer smiles={content[0]} />
    }

    // code文件，使用虚拟滚动
    if (isCodeFile(ext)) {
      return <ContentViewer content={content} mode="code" />
    }

    return <ContentViewer content={content} mode="text" />
  }

  return renderContent()
}

export default FileViewer
