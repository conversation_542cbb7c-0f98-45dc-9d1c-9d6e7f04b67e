export const fileTypes = {
  image: 'image',
  // video: 'video',
  audio: 'audio',
  // word: 'word',
  // excel: 'excel',
  // ppt: 'ppt',
  csv: 'csv',
  pdf: 'pdf',
  code: 'code',
  nii: 'nii',
  molecular: 'molecular',
  text: 'text',
}

export const fileTypesMap: Record<string, string[]> = {
  image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'],
  // video: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'rmvb', '3gp'],
  audio: ['mp3', 'wav', 'aac', 'ogg', 'wma'],
  // word: ['doc', 'docx'],
  // excel: ['xls', 'xlsx'],
  // ppt: ['ppt', 'pptx'],
  csv: ['csv', 'tsv'],
  pdf: ['pdf'],
  code: [
    'js',
    'ts',
    'jsx',
    'tsx',
    'json',
    'html',
    'css',
    'scss',
    'java',
    'py',
    'c',
    'cpp',
    'sh',
    'bat',
  ],
  nii: ['nii', 'nii.gz'],
  molecular: ['smiles'],
  text: ['txt', 'md', 'rtf'],
}

export function getFileTypeExts(fileType: string): string[] {
  fileType = fileType.toLowerCase()
  return fileTypesMap[fileType]
}

export function getFileType(ext: string): string {
  ext = ext.toLowerCase()
  const fileType = Object.keys(fileTypesMap).find(key => fileTypesMap[key].includes(ext))
  return fileType ?? fileTypes.text
}

/**
 * 获取文件扩展名
 * @param url 文件路径
 * @returns 文件扩展名
 *
 * example:
 * getExtension('test.nii.gz') => 'nii.gz'
 * getExtension('test.nii') => 'nii'
 * getExtension('test') => ''
 * getExtension('test.') => ''
 * getExtension('test.txt.') => 'txt'
 * getExtension('test.png') => 'png'
 */
export function getExtension(url: string): string {
  // 特殊处理 .nii.gz 扩展名
  if (url.endsWith('.nii.gz')) {
    return 'nii.gz'
  }

  // 普通扩展名处理
  const match = url.match(/(?:\.([a-zA-Z0-9]+))?(?:\?.*)?$/)
  return match && match[1] ? match[1] : ''
}
