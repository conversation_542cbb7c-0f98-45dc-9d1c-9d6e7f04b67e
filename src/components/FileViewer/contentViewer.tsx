import AutoSizer from 'react-virtualized-auto-sizer'
import { FixedSizeList } from 'react-window'
import { useRef } from 'react'
import Markdown from '@/components/Markdown'

// 每行高度（像素）
const LINE_HEIGHT = 20
// 行组件属性类型
type RowProps = {
  data: string[]
  index: number
  style: React.CSSProperties
}

const CodeViewer = ({ content, mode }: { content: string[]; mode: 'code' | 'text' }) => {
  const listRef = useRef<FixedSizeList<string[]>>(null)

  // 虚拟滚动行组件
  const codeRow = ({ data, index, style }: RowProps) => (
    <div style={style} className="whitespace-pre">
      <span className="text-gray-500 text-xs mr-2 select-none">{index + 1}</span>
      <span className="whitespace-pre">{data[index]}</span>
    </div>
  )

  const textRow = ({ data, index, style }: RowProps) => (
    <div style={style}>
      <Markdown>{data[index]}</Markdown>
    </div>
  )

  return (
    <div className="h-full w-full">
      <AutoSizer>
        {({ height, width }) => (
          <FixedSizeList
            ref={listRef}
            height={height}
            itemCount={content.length}
            itemSize={LINE_HEIGHT}
            width={width}
            itemData={content}
            className="font-mono text-sm"
          >
            {mode === 'code' ? codeRow : textRow}
          </FixedSizeList>
        )}
      </AutoSizer>
    </div>
  )
}

export default CodeViewer
