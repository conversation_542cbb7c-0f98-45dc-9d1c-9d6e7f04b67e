.NiiControl {
  display: flex;
  align-items: center;
  flex: 1;
}

.NiiControlButton {
  padding: 8px 12px;
  background-color: #4caf50;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;

  :hover {
    background-color: #45a049;
  }
}
.NiiControlInput {
  padding: 8px 12px;
  background-color: #333;
  color: #e0e0e0;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: default;
  border: initial;
  outline: none;
  height: 5px;
  border-radius: 5px;
  transition: opacity 0.2s;
  cursor: pointer;
}
.NiiControlLabel {
  margin-right: 10px;
  font-weight: 500;
  color: #e0e0e0;
}
.NiiControls {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 20px 0;
}
.NiiSliceView {
  background-color: #1e1e1e;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);

  .ant-image-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.NiiSliceViewRow {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.NiiViewerContainer {
  max-width: 100%;
  height: auto;
  margin: 0;
  padding: 0px 20px 20px;
  background-color: #121212;
  color: #e0e0e0;
  margin-bottom: 20px;
}
.NiiSliceSpin {
  display: flex;
  justify-content: center;
  align-items: center;
}

.NiiSliceViewEmpty {
  display: flex;
  justify-content: center;
  align-items: center;
}
.NiiSliceViewLoading {
  display: flex;
  justify-content: center;
  align-items: center;
}
