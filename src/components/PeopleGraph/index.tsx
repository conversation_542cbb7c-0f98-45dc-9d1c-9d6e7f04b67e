import { useState } from 'react'
import './index.css'
import BgSvg from '@/assets/dataset-bg.svg'
import PeopleSvg from '@/assets/dataset-people.svg'
import BrainSvg from '@/assets/dataset/brain.svg'
import HeartSvg from '@/assets/dataset/heart.svg'
import LungSvg from '@/assets/dataset/lung.svg'
import StomachSvg from '@/assets/dataset/stomach.svg'
import LiverSvg from '@/assets/dataset/liver.svg'
import BrainLinkSvg from '@/assets/dataset/brain-link.svg'
import HeartLinkSvg from '@/assets/dataset/heart-link.svg'
import LungLinkSvg from '@/assets/dataset/lung-link.svg'
import StomachLinkSvg from '@/assets/dataset/stomach-link.svg'
import LiverLinkSvg from '@/assets/dataset/liver-link.svg'
import DownSvg from '@/assets/dataset/down.svg'

type OrganType = 'brain' | 'heart' | 'lung' | 'stomach' | 'liver'
const organMap: Record<OrganType, string> = {
  brain: 'Brain',
  heart: 'Heart',
  lung: 'Lung',
  stomach: 'Stomach',
  liver: 'Liver',
}

export default function PeopleGraph({ data }: { data: any }) {
  // 在组件顶部添加状态管理折叠状态
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({})

  // 切换文件夹展开/折叠状态
  const toggleFolder = (folderId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId],
    }))
  }

  // 递归渲染文件夹和文件组件
  const renderTree = (
    node: any,
    level: number = 0,
    isRoot: boolean = false,
    organType: OrganType,
  ) => {
    if (!node) return null

    // 如果是根节点的子节点，跳过根节点渲染
    if (isRoot && node.isFolder && node.children) {
      return (
        <div key={node.id} className="w-auto">
          {node.children.map((child: any) => renderTree(child, level, false, organType))}
        </div>
      )
    }

    // 如果是文件夹
    if (node.isFolder && node.children) {
      const isExpanded = expandedFolders[node.id] || false
      const basePadding = 6 // 基础缩进
      const levelPadding = level * 20 // 每级缩进20px

      return (
        <div key={node.id} className="w-auto">
          <div
            className="flex items-center py-1 w-auto hover:bg-gray-50 rounded cursor-pointer"
            style={{ paddingLeft: `${basePadding + levelPadding}px` }}
            onClick={e => toggleFolder(node.id, e)}
          >
            <span
              className={`transform transition-transform mr-2 ${isExpanded ? 'rotate-360' : 'rotate-270'}`}
            >
              <img src={DownSvg} alt="down" width={16} height={16} />
            </span>
            <span className="text-[#000000D9] truncate">{node.name}</span>
          </div>
          {isExpanded && (
            <div className="w-auto">
              {node.children.map((child: any) => renderTree(child, level + 1, false, organType))}
            </div>
          )}
        </div>
      )
    }

    // 如果是文件
    const handleFileClick = (e: React.MouseEvent<HTMLDivElement>) => {
      e.stopPropagation()
      window.open(`/dataset-detail?id=${node.id}`, '_blank')
    }

    // 计算左边距，确保与父级文本对齐
    const basePadding = 6 // 基础缩进
    const levelPadding = level * 20 // 每级缩进20px
    const iconWidth = 16 // 图标宽度
    const iconMargin = 8 // 图标右边距
    const totalPadding = basePadding + levelPadding + iconWidth + iconMargin

    return (
      <div
        key={node.id}
        className="py-1 w-auto hover:bg-gray-50 rounded"
        style={{ paddingLeft: `${totalPadding}px` }}
        data-node={JSON.stringify(node)}
        onClick={handleFileClick}
      >
        <div className="flex items-center text-[#000000D9] w-auto cursor-pointer">
          <span className="text-sm truncate">{node.name}</span>
        </div>
      </div>
    )
  }

  // 修改 renderDetail 函数
  const renderDetail = (type: OrganType) => {
    const organName = organMap[type]
    const organData = data?.find((item: any) => item.name === organName)

    if (!organData) return null

    return (
      <div className="absolute top-0 left-[90px] z-10">
        <div className="w-[300px] flex flex-col p-4 gap-2 bg-white/90 border border-[#EDF1F7] rounded-lg shadow-lg">
          <div className="flex flex-col gap-2 max-h-[300px] overflow-y-auto">
            {organData.children?.map((child: any) => renderTree(child, 0, false, type))}
          </div>
        </div>
      </div>
    )
  }

  // 渲染器官容器
  const renderOrgan = (type: OrganType, icon: any, label: string, position: string) => {
    return (
      <div className={`${position} organ-container`}>
        <img src={icon} alt={type} width={50} height={50} />
        <span className="organ-label">{label}</span>
        {renderDetail(type)}
      </div>
    )
  }

  return (
    <div className="w-full flex justify-center people-graph">
      {/* Background */}
      <img src={BgSvg} alt="bg" width={800} height={800} className="absolute top-[40px]" />
      <div className="w-[592px] h-[569px] flex items-center justify-center relative">
        {/* People silhouette */}
        <img
          src={PeopleSvg}
          alt="people"
          width={258}
          height={493}
          className="absolute top-[153px]"
        />

        {/* 器官和详情 */}
        {renderOrgan('brain', BrainSvg, 'Brain', 'top-[77px] left-[409px]')}
        <img src={BrainLinkSvg} alt="brain-link" className="top-[126px] right-[183px] organ-link" />

        {renderOrgan('heart', HeartSvg, 'Heart', 'top-[167px] left-[518px]')}
        <img
          src={HeartLinkSvg}
          alt="heart-link"
          width={241}
          height={94}
          className="top-[215px] left-[278px] organ-link"
        />

        {renderOrgan('lung', LungSvg, 'Lung', 'top-[307px] left-[452px]')}
        <img src={LungLinkSvg} alt="lung-link" className="top-[344px] left-[278px] organ-link" />

        {renderOrgan('liver', LiverSvg, 'Liver', 'top-[298px] left-[26px]')}
        <img src={LiverLinkSvg} alt="liver-link" className="top-[346px] left-[116px] organ-link" />

        {renderOrgan('stomach', StomachSvg, 'Stomach', 'top-[371px] left-[116px]')}
        <img
          src={StomachLinkSvg}
          alt="stomach-link"
          className="top-[409px] left-[206px] organ-link"
        />
      </div>
    </div>
  )
}
