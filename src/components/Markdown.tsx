import ReactMarkdown from 'react-markdown'
import rehypeKatex from 'rehype-katex'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'

const Markdown = ({ children, className }: { children: string; className?: string }) => {
  const defaultClassNames = 'font-normal !text-[14px] my-1 whitespace-nowrap'
  return (
    <ReactMarkdown
      children={children || ''}
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[rehypeRaw, rehypeKatex]}
      components={{
        p: ({ children }) => <p className={`${defaultClassNames} ${className}`}>{children}</p>,
        h1: ({ children }) => (
          <h1 className={`${defaultClassNames} !text-[20px] ${className}`}>{children}</h1>
        ),
        h2: ({ children }) => (
          <h2 className={`${defaultClassNames} !text-[16px] ${className}`}>{children}</h2>
        ),
        h3: ({ children }) => <h3 className={`${defaultClassNames} ${className}`}>{children}</h3>,
        ul: ({ children }) => <ul className={`${defaultClassNames} ${className}`}>{children}</ul>,
        ol: ({ children }) => <ol className={`${defaultClassNames} ${className}`}>{children}</ol>,
        li: ({ children }) => <li className={`${defaultClassNames} ${className}`}>{children}</li>,
        img: ({ src, alt, ...props }) => (
          <img src={src} alt={alt || 'Image'} style={{ maxWidth: '100%' }} {...props} />
        ),
        table: ({ children, ...props }) => (
          <div className="table-container my-4 overflow-x-auto">
            <table {...props} className="w-full border-collapse border border-gray-200">
              {children}
            </table>
          </div>
        ),
        tr: ({ children, ...props }) => <tr {...props}>{children}</tr>,
        th: ({ children, ...props }) => (
          <th
            {...props}
            className="border border-gray-200 bg-gray-50 px-4 py-2 text-left font-medium text-gray-700"
          >
            {children}
          </th>
        ),
        td: ({ children, ...props }) => (
          <td {...props} className="border border-gray-200 px-4 py-2 text-gray-700">
            {children}
          </td>
        ),
      }}
    />
  )
}

export default Markdown
