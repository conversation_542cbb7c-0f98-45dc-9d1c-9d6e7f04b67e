import { Spin } from 'antd'

const Loading = ({ width, height, top }: { width?: number; height?: number; top?: number }) => {
  // 'calc(100vh - 89px)'
  const style: React.CSSProperties = {}
  if (width) {
    style.width = `${width}px`
  }
  if (height) {
    style.height = `${height}px`
  }
  if (top) {
    style.top = `${top}px`
  }
  return (
    <div className="w-full h-full left-0 bg-[#FFFFFF80] z-50 absolute" style={style}>
      <div className="h-full flex items-center justify-center">
        <Spin />
      </div>
    </div>
  )
}

export default Loading
