export interface Response {
  /**
   * 数据
   */
  list: Data[]
  /**
   * 分页信息
   */
  pagination: Pagination
  [property: string]: any
}

/**
 * 分页信息
 */
export interface Pagination {
  /**
   * 当前页码
   */
  pageNum: number
  /**
   * 每页显示数量
   */
  pageSize: number
  /**
   * 总数
   */
  total: number
  /**
   * 总页数
   */
  pages: number
}

/**
 * 数据
 */
export interface Data {
  /**
   * 数据集描述
   */
  datasetDescription: string
  /**
   * 数据集名称
   */
  datasetName: string
  /**
   * 数据集容量
   */
  datasetSize: string
  /**
   * 特性
   */
  features: Feature[]
  /**
   * 数据集id, 唯一标识符，由于可能超出JavaScript安全整数范围，故以字符串形式提供。
   */
  id: string
  /**
   * 许可协议
   */
  license: string
  /**
   * 许可协议url
   */
  licenseUrl: string
  /**
   * 负责人
   */
  owner: string
  [property: string]: any
}

export interface Feature {
  /**
   * 特性名
   */
  featureName: string
  /**
   * 特性类型
   */
  featureType: string
  /**
   * 学科
   */
  subject: string
  [property: string]: any
}

export interface License {
  name: string
  content: string
}

export interface File {
  id: string
  fileName: string
  filePath: string
  fileSize: string
  fileType: string
  creAt: string
  updAt: string
}
